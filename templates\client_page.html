<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Page</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Empty container styles */
        .drafts-empty-container,
        .jobs-empty-container,
        .contracts-empty-container,
        .paused-empty-container {
            padding: 1.2rem;
            background-color: #f9fafc;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            margin-top: 0.8rem;
        }

        /* Scrollable containers */
        .drafts-scroll-container,
        .jobs-scroll-container {
            max-height: 600px;
            overflow-y: auto;
            padding-right: 10px;
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 74, 173, 0.5) rgba(0, 0, 0, 0.1);
        }

        .drafts-scroll-container::-webkit-scrollbar,
        .jobs-scroll-container::-webkit-scrollbar { 
            width: 6px;
        }

        .drafts-scroll-container::-webkit-scrollbar-track,
        .jobs-scroll-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 10px;
        }

        .drafts-scroll-container::-webkit-scrollbar-thumb,
        .jobs-scroll-container::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, rgba(0, 74, 173, 0.5), rgba(205, 32, 139, 0.5));
            border-radius: 10px;
        }

        .drafts-scroll-container::-webkit-scrollbar-thumb:hover,
        .jobs-scroll-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, rgba(0, 74, 173, 0.7), rgba(205, 32, 139, 0.7));
        }

        .content-wrapper {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            width: 100%;
        }

        .draft-content {
            display: flex !important;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 1rem;
            background-color: white;
            border-radius: 8px;
            margin-bottom: 0.8rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 74, 173, 0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .draft-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            opacity: 0.9;
        }

        .draft-content:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
            border-color: rgba(0, 74, 173, 0.3);
            background-color: #fafcff;
        }

        .draft-content:hover::before {
            width: 6px;
            opacity: 1;
        }

        .draft-content:hover .fill-draft-btn {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 6px 15px rgba(0, 74, 173, 0.25);
            }
            50% {
                box-shadow: 0 6px 25px rgba(205, 32, 139, 0.5);
            }
            100% {
                box-shadow: 0 6px 15px rgba(0, 74, 173, 0.25);
            }
        }

        .draft-info {
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        .draft-info .empty-icon {
            font-size: 1.5rem;
            color: var(--primary-blue);
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.1), rgba(205, 32, 139, 0.1));
            padding: 0.6rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 74, 173, 0.15);
        }

        .draft-content:hover .empty-icon {
            transform: scale(1.1);
            color: var(--primary-pink);
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.15), rgba(205, 32, 139, 0.15));
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .draft-text {
            display: flex;
            flex-direction: column;
        }

        .text-group h4 {
            font-size: 1rem;
            font-weight: 600;
            color: var(--primary-blue);
            margin: 0 0 0.2rem 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .text-group p {
            font-size: 0.85rem;
            color: #666;
            margin: 0 0 0.2rem 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .draft-content:hover .text-group h4 {
            color: var(--primary-pink);
        }

        .draft-details {
            font-size: 0.8rem;
            color: var(--primary-pink);
            font-style: italic;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            background: linear-gradient(90deg, rgba(205, 32, 139, 0.1), rgba(0, 74, 173, 0.1));
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
            margin-top: 0.1rem;
        }

        .draft-actions {
            display: flex !important;
            align-items: center;
            gap: 0.6rem;
            z-index: 2;
        }

        /* Draft button styles */
        .fill-draft-btn {
            background: #004AAD; /* Solid blue background instead of gradient */
            color: white;
            border: 2px solid #CD208B; /* Pink border for contrast */
            padding: 0.6rem 1.2rem;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex !important;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 0.9rem;
            box-shadow: 0 6px 15px rgba(0, 74, 173, 0.25);
            text-decoration: none;
            position: relative;
            overflow: hidden;
            letter-spacing: 0.3px;
            text-transform: uppercase;
            min-width: 140px;
            z-index: 2;
            animation: glow 2s infinite alternate;
        }

        @keyframes glow {
            0% {
                box-shadow: 0 8px 20px rgba(0, 74, 173, 0.5);
            }
            50% {
                box-shadow: 0 8px 25px rgba(205, 32, 139, 0.6);
            }
            100% {
                box-shadow: 0 8px 20px rgba(0, 74, 173, 0.5);
            }
        }

        .fill-draft-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.4), rgba(255,255,255,0.1));
            transition: all 0.6s ease;
        }

        .fill-draft-btn::after {
            content: '\f061';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-left: 5px;
            transition: transform 0.3s ease;
        }

        .fill-draft-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 74, 173, 0.4);
            background: #CD208B; /* Pink background on hover */
            border-color: #004AAD; /* Blue border on hover (swapped colors) */
        }

        .fill-draft-btn:hover::before {
            left: 100%;
        }

        .fill-draft-btn:hover::after {
            transform: translateX(3px);
        }

        .fill-draft-btn:active {
            transform: translateY(1px);
            box-shadow: 0 4px 8px rgba(0, 74, 173, 0.3);
        }

        .three-dots-btn {
            background: none;
            border: none;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #777;
        }

        .three-dots-btn:hover {
            background-color: rgba(0, 0, 0, 0.05);
            color: #333;
        }

        /* Empty state message styling */
        .no-drafts-message,
        .no-jobs-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            text-align: center;
            gap: 0.8rem;
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.05), rgba(205, 32, 139, 0.05));
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            margin: 0.8rem 0;
            border: 1px dashed rgba(0, 74, 173, 0.2);
        }

        .no-drafts-message i,
        .no-jobs-message i {
            font-size: 3rem;
            color: var(--primary-blue);
            margin-bottom: 0.8rem;
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.1), rgba(205, 32, 139, 0.1));
            padding: 1.2rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 74, 173, 0.15);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }

        .no-drafts-message:hover i,
        .no-jobs-message:hover i {
            color: var(--primary-pink);
            transform: scale(1.05);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .no-drafts-message p,
        .no-jobs-message p {
            font-size: 1rem;
            color: var(--primary-blue);
            margin-bottom: 1.2rem;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            font-weight: 500;
        }

        /* Job content styling */
        .job-content {
            display: flex !important;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 1rem;
            background-color: white;
            border-radius: 8px;
            margin-bottom: 0.8rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 74, 173, 0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .job-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            opacity: 0.9;
        }

        .job-content:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
            border-color: rgba(0, 74, 173, 0.3);
            background-color: #fafcff;
        }

        .job-content:hover::before {
            width: 6px;
            opacity: 1;
        }

        .job-info {
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        .job-info .empty-icon {
            font-size: 1.5rem;
            color: var(--primary-blue);
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.1), rgba(205, 32, 139, 0.1));
            padding: 0.6rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 74, 173, 0.15);
        }

        .job-content:hover .empty-icon {
            transform: scale(1.1);
            color: var(--primary-pink);
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.15), rgba(205, 32, 139, 0.15));
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .job-text {
            display: flex;
            flex-direction: column;
        }

        .job-details {
            font-size: 0.8rem;
            color: var(--primary-pink);
            font-style: italic;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            background: linear-gradient(90deg, rgba(205, 32, 139, 0.1), rgba(0, 74, 173, 0.1));
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
            margin-top: 0.1rem;
        }

        .job-actions {
            display: flex !important;
            align-items: center;
            gap: 0.6rem;
            z-index: 2;
        }

        .view-job-btn {
            background: #004AAD;
            color: white;
            border: 2px solid #CD208B;
            padding: 0.6rem 1.2rem;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex !important;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 0.9rem;
            box-shadow: 0 6px 15px rgba(0, 74, 173, 0.25);
            text-decoration: none;
            position: relative;
            overflow: hidden;
            letter-spacing: 0.3px;
            text-transform: uppercase;
            min-width: 140px;
            z-index: 2;
        }

        .view-job-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 74, 173, 0.4);
            background: #CD208B;
            border-color: #004AAD;
        }

        .post-job-btn {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 30px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 0.95rem;
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.2);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .post-job-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.4), rgba(255,255,255,0.1));
            transition: all 0.6s ease;
        }

        .post-job-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 74, 173, 0.3);
        }

        .post-job-btn:hover::before {
            left: 100%;
        }

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        html {
            scroll-behavior: smooth; /* Add smooth scrolling */
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        /* Quote Slider Styles */
        .quote-section {
            background: linear-gradient(135deg, var(--primary-blue), #0062d3);
            color: white;
            padding: 2rem 0;
            width: 100%;
            margin-top: 2rem;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 74, 173, 0.15);
            position: relative;
            overflow: hidden;
        }

        .quote-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,0.05)' fill-rule='evenodd'/%3E%3C/svg%3E");
            opacity: 0.5;
        }

        .quote-slider {
            width: 100%;
            max-width: 900px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 1;
        }

        .quote-navigation {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .quote {
            font-size: 1.4rem;
            font-weight: 500;
            text-align: center;
            margin: 0;
            flex: 1;
            padding: 0 2rem;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            line-height: 1.5;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: relative;
            transition: opacity 0.3s ease, transform 0.3s ease;
            opacity: 1;
            transform: translateY(0);
            min-height: 2.5rem;
        }

        .quote::before, .quote::after {
            content: '"';
            font-size: 2.5rem;
            font-family: Georgia, serif;
            position: absolute;
            opacity: 0.3;
        }

        .quote::before {
            left: 0;
            top: -10px;
        }

        .quote::after {
            right: 0;
            bottom: -30px;
        }

        .arrow {
            font-size: 1.8rem;
            cursor: pointer;
            padding: 0.8rem;
            transition: all 0.3s ease;
            user-select: none;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .arrow:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .arrow.left {
            margin-right: 1rem;
        }

        .arrow.right {
            margin-left: 1rem;
        }

        @media (max-width: 768px) {
            .quote-section {
                padding: 1.5rem 0;
                margin-top: 1.5rem;
            }

            .quote-slider {
                padding: 0 1rem;
            }

            .quote {
                font-size: 1.1rem;
                padding: 0 1rem;
            }

            .quote::before, .quote::after {
                font-size: 1.8rem;
            }

            .arrow {
                font-size: 1.2rem;
                padding: 0.5rem;
                width: 40px;
                height: 40px;
            }
        }

        @media (max-width: 480px) {
            .quote-section {
                padding: 1.2rem 0;
            }

            .quote {
                font-size: 1rem;
                padding: 0 0.5rem;
            }

            .arrow {
                width: 35px;
                height: 35px;
            }
        }

        /* Overview Section Styles */
        .overview-section {
            margin-top: 2.5rem;
            padding: 0 1rem;
            position: relative;
        }

        .overview-section::before {
            content: '';
            position: absolute;
            top: -1.5rem;
            left: 1rem;
            right: 1rem;
            height: 1px;
            background: linear-gradient(to right, rgba(0, 74, 173, 0.2), rgba(205, 32, 139, 0.2), rgba(0, 74, 173, 0.2));
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            margin-bottom: 1.8rem;
            position: relative;
        }

        .overview-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
            border-radius: 3px;
        }

        .overview-header h2 {
            font-size: 1.6rem;
            font-weight: 600;
            color: var(--primary-blue);
            margin: 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            position: relative;
            padding-left: 1rem;
        }

        .overview-header h2::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0.3rem;
            bottom: 0.3rem;
            width: 3px;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            border-radius: 3px;
        }

        /* Overview Cards Styles */
        .overview-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .overview-card {
            flex: 1;
            min-width: 180px;
            background-color: white;
            border-radius: 12px;
            padding: 1.2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            border: 1px solid #e0e4e8;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            overflow: hidden;
        }

        .overview-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            opacity: 0.8;
            transition: width 0.3s ease;
        }

        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 74, 173, 0.08), 0 10px 20px rgba(205, 32, 139, 0.05);
            border-color: rgba(0, 74, 173, 0.3);
        }

        .overview-card:hover::before {
            width: 6px;
        }

        .overview-card.active {
            border-color: var(--primary-blue);
            background-color: #f9faff;
        }

        .overview-card.active::before {
            width: 6px;
            opacity: 1;
        }

        .card-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 10px;
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.1), rgba(205, 32, 139, 0.05));
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0, 74, 173, 0.08);
        }

        .overview-card:hover .card-icon {
            transform: scale(1.1);
            box-shadow: 0 6px 15px rgba(0, 74, 173, 0.12);
        }

        .overview-card:nth-child(even) .card-icon {
            background: linear-gradient(135deg, rgba(205, 32, 139, 0.05), rgba(0, 74, 173, 0.1));
        }

        .overview-card i {
            font-size: 1.3rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }

        .overview-card:nth-child(even) i {
            background: linear-gradient(135deg, var(--primary-pink), var(--primary-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }

        .card-content {
            flex: 1;
        }

        .overview-card h3 {
            font-size: 0.9rem;
            font-weight: 600;
            color: #444;
            margin: 0 0 0.3rem 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: color 0.3s ease;
        }

        .overview-card:hover h3 {
            color: var(--primary-blue);
        }

        .overview-card:nth-child(even):hover h3 {
            color: var(--primary-pink);
        }

        .overview-card p {
            font-size: 1.1rem;
            font-weight: 700;
            color: #333;
            margin: 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: color 0.3s ease;
        }

        .overview-card:hover p {
            color: var(--primary-blue);
        }

        .overview-card:nth-child(even):hover p {
            color: var(--primary-pink);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1.2rem;
        }

        /* Priority List Styles */
        .priority-list-container {
            position: relative;
        }

        .priority-list-header {
            display: flex;
            align-items: center;
            gap: 0.6rem;
            padding: 0.6rem 1.2rem;
            background-color: white;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 74, 173, 0.15);
            box-shadow: 0 4px 10px rgba(0,0,0,0.05);
        }

        .priority-list-header:hover {
            background-color: rgba(0, 74, 173, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0, 74, 173, 0.08);
        }

        .priority-list-header h3 {
            font-size: 0.9rem;
            font-weight: 500;
            margin: 0;
            color: var(--primary-blue);
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .priority-icon {
            color: var(--primary-blue);
            font-size: 0.9rem;
            background: rgba(0, 74, 173, 0.1);
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .dropdown-arrow {
            transition: transform 0.3s ease;
            color: var(--primary-blue);
            font-size: 0.8rem;
            margin-left: 0.3rem;
        }

        .priority-list-header:hover .dropdown-arrow {
            transform: rotate(180deg);
        }

        .priority-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 74, 173, 0.1), 0 6px 12px rgba(0, 0, 0, 0.05);
            margin-top: 0.6rem;
            z-index: 100;
            display: none;
            overflow: hidden;
            border: 1px solid rgba(0, 74, 173, 0.1);
        }

        .priority-dropdown.active {
            display: block;
            animation: fadeInDown 0.3s ease;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-item {
            padding: 0.9rem 1.2rem;
            font-size: 0.9rem;
            color: #555;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            display: flex;
            align-items: center;
            gap: 0.6rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item i {
            color: var(--primary-blue);
            font-size: 0.9rem;
            opacity: 0;
            transition: opacity 0.3s ease, transform 0.3s ease;
            transform: translateX(-5px);
        }

        .dropdown-item:hover {
            background-color: rgba(0, 74, 173, 0.05);
            color: var(--primary-blue);
            padding-left: 1.5rem;
        }

        .dropdown-item:hover i {
            opacity: 1;
            transform: translateX(0);
        }

        .dropdown-item.active {
            background-color: rgba(0, 74, 173, 0.08);
            color: var(--primary-blue);
            font-weight: 500;
        }

        .dropdown-item.active i {
            opacity: 1;
            transform: translateX(0);
        }

        /* View Options Styles */
        .view-options {
            display: flex;
            align-items: center;
        }

        .view-toggle {
            display: flex;
            background: linear-gradient(to right, rgba(0, 74, 173, 0.05), rgba(205, 32, 139, 0.05));
            border-radius: 30px;
            overflow: hidden;
            border: 1px solid rgba(0, 74, 173, 0.1);
            box-shadow: 0 4px 10px rgba(0,0,0,0.05);
            padding: 0.3rem;
        }

        .view-btn {
            background: none;
            border: none;
            padding: 0.5rem 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #777;
            position: relative;
            border-radius: 25px;
            margin: 0 0.1rem;
        }

        .view-btn:hover {
            color: var(--primary-blue);
            background-color: rgba(255, 255, 255, 0.6);
        }

        .view-btn.active {
            background-color: white;
            color: var(--primary-blue);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .view-btn:nth-child(even).active {
            color: var(--primary-pink);
        }

        .view-btn i {
            font-size: 1rem;
            transition: transform 0.3s ease;
        }

        .view-btn:hover i {
            transform: scale(1.1);
        }

        /* Tooltip for view buttons */
        .view-btn[data-tooltip]::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0,0,0,0.8);
            color: white;
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            margin-bottom: 0.5rem;
            pointer-events: none;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .view-btn[data-tooltip]:hover::after {
            opacity: 1;
            visibility: visible;
        }

        /* Custom select styling */
        .custom-select-wrapper {
            position: relative;
            user-select: none;
            width: 100%;
        }
        
        .custom-select-trigger {
            position: relative;
            display: block;
            padding: 0.6rem 1rem;
            font-size: 0.9rem;
            color: #333;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
        }
        

        
        .custom-select-options {
            position: absolute;
            display: none;
            top: 100%;
            left: 0;
            right: 0;
            max-height: 300px;
            overflow-y: auto;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 0 0 6px 6px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 100;
        }
        
        .custom-select-wrapper.open .custom-select-options {
            display: block;
        }
        
        .custom-select-option {
            padding: 0.6rem 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .custom-select-option:hover {
            background: #f5f5f5;
        }
        
        .custom-select-option.selected {
            background: rgba(0, 74, 173, 0.1);
            color: var(--primary-blue);
            font-weight: 500;
        }
        
        .custom-select-continent {
            padding: 0.4rem 1rem;
            font-weight: 600;
            color: #666;
            background: #f5f5f5;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        @media (max-width: 768px) {
            .overview-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .header-right {
                width: 100%;
                justify-content: space-between;
            }

            /* Adjust filter and main content for mobile */
            .filters {
                position: static;
                width: 100%;
                max-width: 100%;
                margin-bottom: 20px;
                margin-right: 0;
            }

            main {
                margin-left: 0;
            }
        }

        @media (max-width: 576px) {
        }

        @media (max-width: 480px) {
            .priority-list-header h3 {
                font-size: 0.8rem;
            }

            .priority-list-header {
                padding: 0.4rem 0.8rem;
            }

            .view-btn {
                padding: 0.4rem 0.6rem;
            }

            .view-btn i {
                font-size: 0.9rem;
            }

            .overview-section {
                padding: 0 0.5rem;
            }
        }



        .action-buttons {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            flex: 1;
            min-width: 200px;
            justify-content: flex-end;
        }

        .restart-btn {
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.6rem 1rem;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .restart-btn:hover {
            background-color: #0056b3;
        }

        .icon-btn {
            background: none;
            border: 1px solid #e0e4e8;
            color: #555;
            font-size: 1rem;
            cursor: pointer;
            padding: 0.6rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .icon-btn:hover {
            background-color: #f5f7fa;
            color: var(--primary-blue);
            border-color: #ccd0d5;
        }

        .three-dots-btn {
            background: none;
            border: none;
            color: #777;
            font-size: 1rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .three-dots-btn:hover {
            background-color: #f5f7fa;
            color: #333;
        }

        /* Genius Section Styles */
        .content {
            display: flex;
            gap: 2rem;
            margin-top: 2rem;
            padding: 0 1rem;
            min-height: 100vh; /* Ensure content area is tall enough for scrolling */
            align-items: flex-start; /* Align items to the top */
            width: 100%; /* Ensure content takes full width */
        }

        .content-container {
            position: relative; /* Create positioning context for fixed filter */
            display: flex;
            width: 100%;
            max-width: 100%;
            overflow-x: hidden; /* Prevent horizontal scrolling */
        }

        .filters {
            flex: 0 0 300px; /* Fixed width, don't grow or shrink */
            max-width: 300px;
            min-width: 300px; /* Ensure minimum width */
            background-color: white;
            border-radius: 16px;
            padding: 1.8rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,74,173,0.08);
            /* Position will be controlled by JavaScript */
            transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
            z-index: 1000; /* Higher z-index to ensure it stays above other content */
            margin-right: 20px; /* Add space between filters and content */
            will-change: transform; /* Performance optimization for fixed positioning */
            background-image: linear-gradient(to bottom, rgba(255,255,255,0.9) 0%, rgba(255,255,255,1) 100%);
            backdrop-filter: blur(10px);
            height: fit-content; /* Adjust height based on content */
            display: block !important; /* Ensure it's always displayed */
        }

        /* Class added via JavaScript when filters become sticky */
        .horizontal-filters.is-sticky {
            animation: subtle-bounce 0.5s ease;
            box-shadow: 0 15px 40px rgba(0, 74, 173, 0.08), 0 10px 20px rgba(205, 32, 139, 0.05);
            border-color: #e0e4e8;
            border-radius: 0 0 16px 16px;
            border-top: none;
        }

        .horizontal-filters.is-sticky::before {
            height: 4px;
            opacity: 1;
        }

        @keyframes subtle-bounce {
            0% { transform: translateY(10px); opacity: 0.8; }
            100% { transform: translateY(0); opacity: 1; }
        }

        /* Add a subtle effect when the filters section becomes sticky */
        .filters:hover, .filters:focus-within {
            box-shadow: 0 15px 40px rgba(0, 74, 173, 0.15);
            transform: translateY(-3px);
            border-color: rgba(0, 74, 173, 0.15);
        }

        /* Enhanced styling for filters */
        .filters:hover {
            box-shadow: 0 15px 40px rgba(0, 74, 173, 0.15);
            border-color: rgba(0, 74, 173, 0.15);
            transform: translateY(-3px);
        }

        /* No scrollbar for filters section */

        .filter-header {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 1.2rem;
            position: relative;
        }

        .filter-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
            border-radius: 3px;
        }

        .filter-header i {
            color: var(--primary-blue);
            font-size: 1.3rem;
            background-color: rgba(0,74,173,0.08);
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
        }

        .filters h2 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #222;
            margin: 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            letter-spacing: -0.01em;
        }

        .filter-subtitle {
            font-size: 0.95rem;
            color: #666;
            margin: 0 0 2rem 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            line-height: 1.5;
            padding-top: 0.5rem;
        }

        .filter-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .filter-label {
            display: flex;
            align-items: center;
            gap: 0.6rem;
            font-size: 0.95rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.6rem;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .filter-label i {
            color: var(--primary-blue);
            font-size: 1rem;
            width: 18px;
            text-align: center;
            opacity: 0.9;
        }

        .filter-select {
            width: 100%;
            padding: 0.9rem 1rem;
            border: 1px solid #e0e4e8;
            border-radius: 10px;
            font-size: 0.95rem;
            color: #444;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            background-color: white;
            transition: all 0.2s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }

        .filter-select:focus, .price-input:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
            outline: none;
        }

        .filter-select:hover, .price-input:hover {
            border-color: #b0b7c3;
        }

        .price-input {
            width: 100%;
            padding: 0.9rem 1rem;
            border: 1px solid #e0e4e8;
            border-radius: 10px;
            font-size: 0.95rem;
            color: #444;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            background-color: white;
            transition: all 0.2s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
        }

        .reset-filters-btn {
            width: 100%;
            padding: 0.9rem;
            background-color: #f5f7fa;
            color: #555;
            border: 1px solid #e0e4e8;
            border-radius: 10px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.6rem;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            margin-top: 1.5rem;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
        }

        .reset-filters-btn:hover {
            background-color: #e9ecf1;
            color: #333;
            border-color: #cfd4dd;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .reset-filters-btn i {
            font-size: 0.95rem;
            transition: transform 0.3s ease;
        }

        .reset-filters-btn:hover i {
            transform: rotate(90deg);
        }

        .post-job-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.6rem;
            background: linear-gradient(135deg, var(--primary-blue), #0062d3);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 1rem 1.2rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            box-shadow: 0 4px 12px rgba(0,74,173,0.2);
            position: relative;
            overflow: hidden;
            margin-top: 1rem;
        }

        .post-job-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.6s ease;
        }

        .post-job-btn:hover {
            background: linear-gradient(135deg, #003d91, #0057c3);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,74,173,0.3);
        }

        .post-job-btn:hover::before {
            left: 100%;
        }

        main {
            flex: 1;
            min-width: 0; /* Prevent overflow issues */
            width: 100%; /* Take up full width */
        }

        /* Horizontal Filters */
        .horizontal-filters {
            width: 100%;
            background-color: white;
            border-radius: 16px;
            padding: 1.2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            border: 1px solid #e0e4e8;
            margin-top: 0;
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
            display: block !important; /* Ensure it's always displayed */
        }

        .horizontal-filters::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
            opacity: 0.8;
        }

        .filter-header-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.8rem;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .filter-header-row .filter-header {
            margin-bottom: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-header-row .filter-header i {
            color: var(--primary-blue);
            font-size: 1rem;
        }

        .filter-header-row .filter-header h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin: 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .filter-header-row .filter-header::after {
            display: none;
        }

        .filter-header-row .reset-filters-btn {
            width: auto;
            margin-top: 0;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            background-color: white;
            color: var(--primary-pink);
            border: 1px solid rgba(205, 32, 139, 0.2);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.4rem;
        }

        .filter-header-row .reset-filters-btn:hover {
            background-color: rgba(205, 32, 139, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(205, 32, 139, 0.1);
        }

        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            align-items: flex-end;
        }

        .filter-row .filter-group {
            flex: 1 1 auto;
            min-width: 150px;
            max-width: calc(20% - 1rem);
            margin-bottom: 0;
        }

        .filter-label {
            display: flex;
            align-items: center;
            gap: 0.4rem;
            margin-bottom: 0.5rem;
            font-size: 0.85rem;
            font-weight: 600;
            color: #555;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .filter-label i {
            color: var(--primary-blue);
            font-size: 0.9rem;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Alternate filter labels with pink color for visual interest */
        .filter-group:nth-child(even) .filter-label i {
            color: var(--primary-pink);
        }

        .post-job-container {
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
            min-width: 120px;
            max-width: 120px;
        }

        .post-job-container .post-job-btn {
            margin-top: 0;
            width: 100%;
            padding: 0.7rem 1rem;
            font-size: 0.9rem;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0,74,173,0.15);
        }

        .post-job-container .post-job-btn:hover {
            background-color: #003d91;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0,74,173,0.2);
        }

        .price-range {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .price-range .price-input {
            width: calc(50% - 5px);
            padding: 0.7rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 0.85rem;
            transition: all 0.3s ease;
        }

        .price-range .price-input:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
            outline: none;
        }

        .price-separator {
            font-weight: bold;
            color: #666;
        }

        .horizontal-filters .filter-select {
            padding: 0.7rem 1rem;
            font-size: 0.85rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: white;
            color: #444;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            padding-right: 2.5rem;
        }

        .horizontal-filters .filter-select:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
            outline: none;
        }

        /* Alternate focus styles for even filter groups */
        .filter-group:nth-child(even) .filter-select:focus {
            border-color: var(--primary-pink);
            box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
        }

        .filter-group:nth-child(even) .price-input:focus {
            border-color: var(--primary-pink);
            box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
        }

        /* Responsive styles for horizontal filters and genius container */
        @media (max-width: 1200px) {
            .filter-row .filter-group {
                min-width: 120px;
                max-width: calc(33.33% - 1rem);
            }

            .genius-container {
                height: calc(100vh - 280px);
            }
        }

        @media (max-width: 992px) {
            .filter-row .filter-group {
                min-width: 100px;
                max-width: calc(50% - 1rem);
            }

            .post-job-container {
                min-width: 100px;
                max-width: 100px;
            }

            .post-job-container .post-job-btn {
                font-size: 0.8rem;
                padding: 0.6rem 0.8rem;
            }

            .genius-container {
                height: calc(100vh - 350px);
            }
        }

        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                gap: 0.8rem;
            }

            .filter-row .filter-group {
                max-width: 100%;
                width: 100%;
            }

            .post-job-container {
                min-width: 100%;
                max-width: 100%;
                margin-top: 0.5rem;
            }

            .horizontal-filters {
                padding: 1rem;
            }

            .filter-header-row h3 {
                font-size: 1rem;
            }

            .genius-container {
                height: calc(100vh - 450px);
                min-height: 350px;
            }
        }

        .genius-header {
            margin-bottom: 1.5rem;
            position: relative;
            padding-left: 1rem;
        }

        .genius-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0.5rem;
            bottom: 0.5rem;
            width: 3px;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            border-radius: 3px;
        }

        .genius-header h2 {
            font-size: 1.6rem;
            font-weight: 600;
            color: var(--primary-blue);
            margin: 0 0 0.5rem 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: color 0.3s ease;
        }

        .genius-subtitle {
            font-size: 0.95rem;
            color: #666;
            margin: 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            max-width: 80%;
        }

        .genius-subtitle span {
            color: var(--primary-pink);
            font-weight: 500;
        }

        .search-wrapper {
            position: relative;
            margin-bottom: 2rem;
            width: 100%;
        }

        .search {
            width: 100%;
            padding: 0.8rem 1rem 0.8rem 2.5rem;
            border: 1px solid #e0e4e8;
            border-radius: 30px;
            font-size: 0.9rem;
            color: #555;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            background-color: white;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .search:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1), 0 2px 8px rgba(0,0,0,0.05);
        }

        .search:focus + .search-icon {
            color: var(--primary-blue);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #777;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .genius-container {
            height: calc(100vh - 300px); /* Adjust height to leave space for header and filters */
            min-height: 500px; /* Increased minimum height for better profile viewing */
            overflow-y: auto; /* Enable vertical scrolling */
            border-radius: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #e0e4e8;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 30px rgba(0,0,0,0.08);
            position: relative; /* For positioning loading and no results messages */
            transition: all 0.3s ease;
            scroll-behavior: smooth;
            scroll-padding-top: 2rem;
        }

        /* Add scroll indicator */
        .genius-container::after {
            content: '↓ Scroll to see more profiles ↓';
            position: sticky;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(248, 249, 250, 0.95), transparent);
            text-align: center;
            padding: 1rem;
            font-size: 0.85rem;
            color: var(--primary-blue);
            font-weight: 500;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            pointer-events: none;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .genius-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(to right, var(--primary-blue) 30%, var(--primary-pink) 70%);
            border-radius: 20px 20px 0 0;
            opacity: 0.8;
        }

        /* Enhanced scrollbar for the genius container */
        .genius-container::-webkit-scrollbar {
            width: 12px;
        }

        .genius-container::-webkit-scrollbar-track {
            background: rgba(0, 74, 173, 0.08);
            border-radius: 12px;
            margin: 10px 0;
        }

        .genius-container::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, rgba(0, 74, 173, 0.4), rgba(205, 32, 139, 0.4));
            border-radius: 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .genius-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, rgba(0, 74, 173, 0.6), rgba(205, 32, 139, 0.6));
            transform: scale(1.1);
        }

        .genius-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            padding: 0.5rem 0;
        }

        .genius-card {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border-radius: 16px;
            padding: 1.2rem;
            box-shadow: 0 6px 24px rgba(0,0,0,0.06), 0 3px 12px rgba(0,0,0,0.03);
            border: 1px solid rgba(0, 74, 173, 0.1);
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            display: grid;
            grid-template-areas:
                "image header header"
                "image details details"
                "actions actions actions";
            grid-template-columns: auto 1fr auto;
            grid-template-rows: auto 1fr auto;
            gap: 1rem;
            position: relative;
            overflow: hidden;
            margin-bottom: 0;
            scroll-margin-top: 2rem;
            height: fit-content;
            min-height: 200px;
        }

        .genius-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            opacity: 0.9;
            transition: all 0.4s ease;
            border-radius: 20px 0 0 20px;
        }

        .genius-card:hover {
            transform: translateY(-6px) scale(1.01);
            box-shadow: 0 16px 32px rgba(0, 74, 173, 0.1), 0 12px 20px rgba(205, 32, 139, 0.06);
            border-color: rgba(0, 74, 173, 0.3);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
        }

        .genius-card:hover::before {
            width: 8px;
            opacity: 1;
        }

        .expert-badge, .entry-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.3rem 0.6rem;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            z-index: 2;
        }

        .expert-badge {
            background-color: rgba(0, 74, 173, 0.1);
            color: var(--primary-blue);
            border: 1px solid rgba(0, 74, 173, 0.2);
        }

        .entry-badge {
            background-color: rgba(0, 74, 173, 0.1);
            color: var(--primary-blue);
            border: 1px solid rgba(0, 74, 173, 0.2);
        }

        .genius-card:hover .expert-badge,
        .genius-card:hover .entry-badge {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.15);
        }

        .genius-header {
            grid-area: header;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .genius-info {
            grid-area: details;
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
        }

        .genius-image {
            grid-area: image;
            width: 80px;
            height: 80px;
            flex-shrink: 0;
            position: relative;
            margin: 0;
        }

        .genius-image::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            opacity: 0;
            z-index: 0;
            transition: opacity 0.3s ease;
        }

        .genius-card:hover .genius-image::after {
            opacity: 0.1;
        }

        .genius-image img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid rgba(0, 74, 173, 0.2);
            box-shadow: 0 6px 16px rgba(0, 74, 173, 0.15);
            transition: all 0.4s ease;
            position: relative;
            z-index: 1;
        }

        .genius-card:hover .genius-image img {
            border-color: var(--primary-blue);
            transform: scale(1.05);
            box-shadow: 0 10px 24px rgba(0, 74, 173, 0.25);
        }

        .genius-card h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease;
            line-height: 1.3;
            text-align: left;
        }

        .genius-card:hover h3 {
            color: var(--primary-blue);
        }

        .genius-description {
            color: #5a6c7d;
            font-size: 0.85rem;
            line-height: 1.4;
            position: relative;
            transition: all 0.3s ease;
            text-align: left;
            height: 3.5em;
            display: flex;
            flex-direction: column;
        }

        .genius-description .introduction-text {
            max-height: 2.8em;
            overflow: hidden;
            transition: all 0.3s ease;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            flex: 1;
            font-size: 0.85rem;
        }

        .genius-description .introduction-text.expanded {
            max-height: 2.8em;
            overflow: hidden;
            -webkit-line-clamp: unset;
            line-clamp: unset;
            display: block;
            font-size: 0.7rem;
            line-height: 1.2;
        }

        .genius-description p {
            margin: 0;
            font-size: 0.85rem;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .genius-description.expanded {
            max-height: none;
        }

        .genius-description .introduction-text {
            line-height: 1.6;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .genius-description .introduction-text.expanded {
            white-space: normal;
        }

        .genius-description .see-more-btn {
            color: var(--primary-blue);
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: auto;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.2rem;
            transition: all 0.3s ease;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            background: none;
            border: none;
            padding: 0.2rem 0;
            text-decoration: none;
            height: 1.2em;
            align-self: flex-start;
        }

        .genius-description .see-more-btn:hover {
            color: var(--primary-pink);
            text-decoration: underline;
            transform: translateX(1px);
        }

        .genius-description .see-more-btn i {
            font-size: 0.65rem;
            transition: transform 0.3s ease;
        }

        .genius-description .see-more-btn:hover i {
            transform: translateY(1px);
        }

        .genius-description .see-more-btn.expanded i {
            transform: rotate(180deg);
        }

        .genius-card:hover .genius-description {
            color: #444;
        }

        .genius-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.6rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 0.8rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 74, 173, 0.08);
        }

        .genius-card:hover .genius-details {
            background: linear-gradient(135deg, #f0f5fa 0%, #e3f2fd 100%);
            border-color: rgba(0, 74, 173, 0.15);
            transform: translateY(-2px);
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.2rem 0;
        }

        .detail-item i {
            color: var(--primary-blue);
            font-size: 0.8rem;
            width: 16px;
            height: 16px;
            text-align: center;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.1), rgba(0, 74, 173, 0.05));
            border-radius: 50%;
            padding: 0.4rem;
        }

        /* Alternate icons with pink color for visual interest */
        .detail-item:nth-child(even) i {
            color: var(--primary-pink);
            background: linear-gradient(135deg, rgba(205, 32, 139, 0.1), rgba(205, 32, 139, 0.05));
        }

        .genius-card:hover .detail-item i {
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.2);
        }

        .detail-label {
            font-size: 0.7rem;
            font-weight: 600;
            color: #555;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .detail-value {
            font-size: 0.8rem;
            color: #444;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            font-weight: 500;
        }

        .genius-actions {
            grid-area: actions;
            display: flex;
            gap: 0.6rem;
            width: 100%;
        }

        .view-profile-btn, .contact-genius-btn {
            padding: 0.6rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.3rem;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            box-shadow: 0 3px 8px rgba(0,0,0,0.08);
            flex: 1;
        }

        .view-profile-btn {
            background-color: var(--primary-blue);
            color: white;
            border: none;
        }

        .view-profile-btn:hover {
            background-color: #003d91;
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(0,74,173,0.2);
        }

        .contact-genius-btn {
            background-color: white;
            color: var(--primary-pink);
            border: 1px solid var(--primary-pink);
        }

        .contact-genius-btn:hover {
            background-color: var(--primary-pink);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(205, 32, 139, 0.15);
        }

        /* Job Card Styles */
        .job-card {
            background-color: white;
            border-radius: 16px;
            padding: 1.8rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            border: 1px solid #e0e4e8;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            position: relative;
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .job-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            opacity: 0.8;
            transition: width 0.3s ease;
        }

        .job-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 74, 173, 0.08), 0 10px 20px rgba(205, 32, 139, 0.05);
            border-color: rgba(0, 74, 173, 0.3);
            background-color: #fefeff;
        }

        .job-card:hover::before {
            width: 7px;
        }

        .job-badge {
            position: absolute;
            top: 1.2rem;
            right: 1.2rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            background-color: rgba(0, 74, 173, 0.1);
            color: var(--primary-blue);
            border: 1px solid rgba(0, 74, 173, 0.2);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .job-card:hover .job-badge {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.15);
        }

        .job-info {
            width: 100%;
        }

        .job-card h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin: 0 0 1rem 0;
            padding-right: 100px; /* Make room for the badge */
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: color 0.3s ease;
        }

        .job-card:hover h3 {
            color: var(--primary-blue);
        }

        .job-details {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
            background-color: #f9fafc;
            padding: 1rem;
            border-radius: 12px;
            transition: background-color 0.3s ease;
        }

        .job-card:hover .job-details {
            background-color: #f0f5fa;
        }

        .job-detail-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .job-detail-item i {
            color: var(--primary-blue);
            font-size: 0.9rem;
            width: 20px;
            height: 20px;
            text-align: center;
            transition: transform 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 74, 173, 0.08);
            border-radius: 50%;
            padding: 0.5rem;
        }

        .job-detail-item:nth-child(even) i {
            color: var(--primary-pink);
            background-color: rgba(205, 32, 139, 0.08);
        }

        .job-card:hover .job-detail-item i {
            transform: translateY(-2px);
        }

        .job-detail-label {
            font-size: 0.8rem;
            font-weight: 600;
            color: #555;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .job-detail-value {
            font-size: 0.9rem;
            color: #444;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            font-weight: 500;
        }

        .job-description {
            margin-bottom: 1.5rem;
            color: #666;
            line-height: 1.6;
        }

        .job-description p {
            margin: 0;
            font-size: 0.95rem;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .job-actions {
            display: flex;
            gap: 1rem;
        }

        .view-job-btn, .edit-job-btn {
            padding: 0.8rem 1.2rem;
            border-radius: 30px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        .view-job-btn {
            background-color: var(--primary-blue);
            color: white;
            border: none;
        }

        .view-job-btn:hover {
            background-color: #003d91;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0,74,173,0.2);
        }

        .edit-job-btn {
            background-color: white;
            color: var(--primary-pink);
            border: 1px solid var(--primary-pink);
        }

        .edit-job-btn:hover {
            background-color: var(--primary-pink);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(205, 32, 139, 0.15);
        }

        /* Loading and No Results Messages */
        .loading-message, .no-genius-message {
            text-align: center;
            padding: 2.5rem;
            background-color: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 74, 173, 0.08), 0 5px 15px rgba(205, 32, 139, 0.05);
            border: 1px solid #e0e4e8;
            margin: 2rem 0;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: calc(100% - 3rem);
            max-width: 500px;
            z-index: 10;
            overflow: hidden;
        }

        .loading-message::before, .no-genius-message::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
        }

        .loading-message i {
            font-size: 2.5rem;
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
            display: block;
            animation: spin 1.5s infinite linear;
        }

        .no-genius-message i {
            font-size: 2.5rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
            margin-bottom: 1.5rem;
            display: block;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-message h3, .no-genius-message h3 {
            font-size: 1.3rem;
            margin-bottom: 0.8rem;
            color: #333;
            font-weight: 600;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .loading-message p, .no-genius-message p {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.5;
            max-width: 80%;
            margin: 0 auto;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .no-jobs-message {
            text-align: center;
            padding: 3rem 1.5rem;
            background-color: #f9fafc;
            border-radius: 16px;
            border: 1px dashed rgba(0, 74, 173, 0.2);
            position: relative;
            overflow: hidden;
        }

        .no-jobs-message::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
            opacity: 0.7;
        }

        .no-jobs-message i {
            font-size: 3rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
            margin-bottom: 1.5rem;
            display: block;
        }

        .no-jobs-message h3 {
            font-size: 1.4rem;
            color: var(--primary-blue);
            margin-bottom: 0.8rem;
            font-weight: 600;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .no-jobs-message p {
            color: #666;
            margin-bottom: 1.8rem;
            font-size: 0.95rem;
            line-height: 1.5;
            max-width: 80%;
            margin-left: auto;
            margin-right: auto;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .post-job-btn {
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 30px;
            padding: 0.8rem 1.5rem;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            box-shadow: 0 4px 10px rgba(0,74,173,0.15);
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .post-job-btn:hover {
            background-color: #003d91;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0,74,173,0.2);
        }

        .no-jobs-message .post-job-btn {
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
        }

        .no-jobs-message .post-job-btn:hover {
            background: linear-gradient(to right, #003d91, #b41973);
        }

        @media (max-width: 992px) {
            .content {
                flex-direction: column;
            }

            .filters {
                max-width: 100%;
                margin-bottom: 1.5rem;
                position: relative; /* Remove sticky positioning on mobile */
                top: 0;
                max-height: none;
                overflow-y: visible;
                margin-right: 0;
            }

            .genius-details {
                grid-template-columns: 1fr 1fr;
            }

            .job-details {
                grid-template-columns: 1fr 1fr;
            }
        }

        /* Add a special class for tablet devices to make filters sticky horizontally */
        @media (min-width: 768px) and (max-width: 992px) {
            .filters {
                position: sticky;
                top: 20px;
                z-index: 100;
                max-height: none;
            }
        }

        @media (max-width: 768px) {
            .genius-grid {
                grid-template-columns: 1fr;
                gap: 1.2rem;
            }

            .genius-card {
                padding: 1rem;
                grid-template-areas:
                    "image header"
                    "details details"
                    "actions actions";
                grid-template-columns: auto 1fr;
                min-height: 180px;
            }

            .genius-image {
                width: 70px;
                height: 70px;
            }

            .genius-card h3 {
                font-size: 1rem;
            }

            .genius-description {
                font-size: 0.8rem;
            }

            .genius-description .introduction-text {
                max-height: 2.4em;
            }

            .genius-description .see-more-btn {
                font-size: 0.7rem;
                margin-top: 0.2rem;
            }

            .genius-details {
                padding: 0.6rem;
                gap: 0.4rem;
                grid-template-columns: 1fr;
            }

            .detail-item {
                justify-content: flex-start;
            }

            .genius-actions {
                gap: 0.5rem;
                flex-direction: column;
            }

            .view-profile-btn, .contact-genius-btn {
                padding: 0.5rem 0.7rem;
                font-size: 0.75rem;
            }

            .expert-badge, .entry-badge {
                top: 0.6rem;
                right: 0.6rem;
                font-size: 0.6rem;
                padding: 0.2rem 0.4rem;
            }
        }

        @media (min-width: 769px) and (max-width: 992px) {
            .genius-card {
                padding: 1rem;
                min-height: 190px;
            }

            .genius-image {
                width: 75px;
                height: 75px;
            }

            .genius-card h3 {
                font-size: 1rem;
            }

            .genius-description {
                font-size: 0.8rem;
            }

            .genius-details {
                padding: 0.7rem;
                gap: 0.5rem;
            }

            .view-profile-btn, .contact-genius-btn {
                padding: 0.55rem 0.8rem;
                font-size: 0.75rem;
            }
        }

            .job-details {
                grid-template-columns: 1fr;
            }

            .job-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .view-job-btn, .edit-job-btn {
                width: 100%;
                justify-content: center;
            }

        /* Container Styles */
        .container {
            width: 100%;
            max-width: 2000px;
            margin: 0;
            padding: 0;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 4.5rem;
            position: relative;
            flex-wrap: wrap;
            width: 100%;
        }

        /* Mobile menu button */
        .mobile-menu-btn {
            display: none; /* Hidden by default (desktop view) */
            background: none;
            border: none;
            color: var(--primary-blue);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            margin-right: 0.5rem;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            color: var(--primary-pink);
            background-color: rgba(0, 74, 173, 0.05);
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding-left: 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            color: var(--primary-pink);
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            color: var(--primary-pink);
        }

        .logo:hover, .logo:active {
            color: var(--primary-blue);
        }

        .logo:hover h1 {
            color: var(--primary-blue);
        }

        .nav-links {
            display: flex;
            gap: 0;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 0.5rem;
            font-size: 1rem;
            font-weight: 500;
            position: relative;
            white-space: nowrap;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .nav-links a:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-pink);
            transition: width 0.3s ease;
        }

        .nav-links a:hover:after, .nav-links a.active:after {
            width: 100%;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
            margin: 0;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 0.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            position: relative;
            white-space: nowrap;
        }

        .nav-dropbtn:hover {
            color: var(--primary-pink);
        }

        .nav-dropbtn:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-pink);
            transition: width 0.3s ease;
        }

        .nav-dropbtn:hover:after {
            width: 100%;
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
            margin-top: 0.5rem;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 0.9rem;
        }

        .nav-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .nav-dropdown-content a:after {
            display: none;
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding-right: 1rem;
            height: 100%;
        }

        /* Search container */
        .search-container {
            display: flex;
            align-items: center;
            margin-right: 1.5rem;
        }

        @media (max-width: 992px) {
            .right-section {
                gap: 1rem;
            }

            .search-container {
                margin-right: 1rem;
            }
        }

        .search-bar {
            height: 40px;
            display: flex;
            align-items: center;
            background: #f5f7fa;
            border: 1px solid #e0e4e8;
            border-radius: 20px;
            width: 220px;
            transition: all 0.3s ease;
        }

        .search-bar:focus-within {
            background: white;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        @media (max-width: 768px) {
            .search-bar {
                width: 180px;
            }
        }

        @media (max-width: 576px) {
            .search-bar {
                width: 150px;
            }
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 0.5rem 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 0.9rem;
            background: transparent;
        }

        .search-bar .icon {
            color: #8a94a6;
            padding: 0 1rem 0 0.5rem;
            font-size: 0.9rem;
        }

        .search-bar:focus-within .icon {
            color: var(--primary-blue);
        }


        /* Auth buttons container */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem; /* Increased gap between notification and profile */
        }

        /* Enhanced Notification Styles */
        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-icon:hover {
            background-color: rgba(0, 74, 173, 0.1);
        }

        .notification-icon i {
            font-size: 1.3rem;
            color: #4a5568;
        }

        #notification-count {
            position: absolute;
            top: 2px;
            right: 2px;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            min-width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 600;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .notification-dropdown {
            position: absolute;
            top: 60px;
            right: 10px;
            width: 380px;
            max-height: 500px;
            overflow-y: auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            border: 1px solid rgba(0,0,0,0.08);
            animation: dropdown-fade 0.2s ease-out;
        }

        @media (max-width: 576px) {
            .notification-dropdown {
                width: calc(100vw - 40px);
                right: -100px;
                max-height: 400px;
            }
        }

        @media (max-width: 480px) {
            .notification-dropdown {
                right: -150px;
            }
        }

        @keyframes dropdown-fade {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .notification-dropdown.show {
            display: block;
        }

        .notification-header {
            padding: 18px 20px;
            border-bottom: 1px solid rgba(0,0,0,0.08);
            font-weight: 600;
            font-size: 1rem;
            color: var(--neutral-900);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header-actions {
            font-size: 0.8rem;
            color: var(--primary-blue);
            cursor: pointer;
        }

        .notification-item {
            padding: 16px 20px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            display: flex;
            align-items: flex-start;
            transition: all 0.2s;
        }

        .notification-item:hover {
            background-color: rgba(0,0,0,0.02);
        }

        .notification-item.unread {
            background-color: rgba(0, 74, 173, 0.05);
        }

        .notification-item.unread:hover {
            background-color: rgba(0, 74, 173, 0.08);
        }

        .notification-icon-wrapper {
            margin-right: 15px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-light-blue), var(--primary-light-pink));
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .notification-item i {
            color: var(--primary-blue);
            font-size: 1.2rem;
        }

        .notification-content {
            flex: 1;
        }

        .notification-content p {
            margin: 0 0 6px 0;
            line-height: 1.4;
            color: var(--neutral-800);
            font-size: 0.9rem;
        }

        .notification-content p strong {
            color: var(--neutral-900);
            font-weight: 600;
        }

        .notification-content span {
            font-size: 0.75rem;
            color: var(--neutral-600);
            display: block;
        }

        .notification-actions {
            display: flex;
            margin-top: 12px;
            gap: 10px;
        }

        .accept-btn, .reject-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .accept-btn {
            background-color: var(--primary-blue);
            color: white;
            box-shadow: 0 2px 4px rgba(0, 74, 173, 0.2);
        }

        .accept-btn:hover {
            background-color: #003d8f;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 74, 173, 0.3);
        }

        .reject-btn {
            background-color: white;
            color: var(--neutral-700);
            border: 1px solid var(--neutral-400);
        }

        .reject-btn:hover {
            background-color: var(--neutral-200);
            color: var(--neutral-900);
        }

        .notification-status {
            font-size: 0.75rem;
            padding: 4px 10px;
            border-radius: 20px;
            margin-top: 8px;
            display: inline-block;
            font-weight: 500;
        }

        .notification-status.accepted {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .notification-status.rejected {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error);
        }

        .empty-notifications {
            padding: 40px 20px;
            text-align: center;
            color: var(--neutral-600);
        }

        .empty-notifications i {
            font-size: 2.5rem;
            color: var(--neutral-400);
            margin-bottom: 15px;
        }

        .empty-notifications p {
            font-size: 0.9rem;
        }

        .view-all {
            display: block;
            text-align: center;
            padding: 15px;
            color: #004AAD;
            text-decoration: none;
            font-weight: 500;
            border-top: 1px solid #eee;
        }

        /* Profile button */
        .profile-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid white;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .profile-button:hover {
            border-color: var(--primary-blue);
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0,0,0,0.15);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 60px;
            background-color: #fff;
            min-width: 220px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .profile-dropdown-content a i {
            width: 20px;
            text-align: center;
        }

        .profile-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown.active .profile-dropdown-content {
            display: block;
        }

        /* Professional Client Profile Section */
        .profile-section {
            margin: 1.5rem 0 2.5rem;
            background-color: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 74, 173, 0.08), 0 5px 15px rgba(205, 32, 139, 0.05);
            overflow: hidden;
            width: 100%;
            position: relative;
        }

        .profile-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
            z-index: 1;
        }

        .profile-header {
            position: relative;
        }

        .profile-banner {
            height: 140px;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            position: relative;
            overflow: hidden;
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,.15)' fill-rule='evenodd'/%3E%3C/svg%3E");
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .profile-banner::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(to top, rgba(255,255,255,0.8), transparent);
        }

        .welcome {
            display: flex;
            padding: 0 2rem 2rem;
            margin-top: -50px;
            flex-wrap: wrap;
            position: relative;
            z-index: 2;
        }

        .profile-photo-container {
            position: relative;
            margin-right: 2rem;
        }

        .profile-photo-container img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 4px solid white;
            object-fit: cover;
            background-color: #f0f0f0;
            box-shadow: 0 8px 20px rgba(0, 74, 173, 0.15), 0 4px 10px rgba(205, 32, 139, 0.1);
            transition: all 0.3s ease;
        }

        .profile-photo-container:hover img {
            transform: scale(1.05);
            box-shadow: 0 12px 25px rgba(0, 74, 173, 0.2), 0 8px 15px rgba(205, 32, 139, 0.15);
        }

        /* Online status indicator removed */

        .info {
            flex: 1;
            padding-top: 60px;
            min-width: 250px;
        }

        .name-badge {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .name-badge h2 {
            margin: 0;
            font-size: 1.7rem;
            color: #333;
            font-weight: 600;
            margin-right: 1.2rem;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .name-badge h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
            border-radius: 3px;
        }

        .name-badge h2 span {
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }

        .client-badge {
            background: linear-gradient(to right, rgba(0, 74, 173, 0.1), rgba(205, 32, 139, 0.1));
            color: var(--primary-blue);
            font-size: 0.85rem;
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            border: 1px solid rgba(0, 74, 173, 0.2);
            display: inline-block;
            margin-top: 0.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .client-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.1), 0 2px 6px rgba(205, 32, 139, 0.1);
            background: linear-gradient(to right, rgba(0, 74, 173, 0.15), rgba(205, 32, 139, 0.15));
        }

        .client-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.2rem;
            margin-bottom: 2rem;
            padding-bottom: 1.8rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background-color: #f9fafc;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: inset 0 2px 8px rgba(0,0,0,0.03);
        }

        .client-details p {
            margin: 0;
            display: flex;
            align-items: center;
            color: #555;
            font-size: 0.95rem;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease;
        }

        .client-details p:hover {
            transform: translateX(5px);
            color: #333;
        }

        .client-details p:hover i {
            transform: scale(1.2);
            opacity: 1;
        }

        .client-details i {
            margin-right: 0.8rem;
            color: var(--primary-blue);
            width: 20px;
            height: 20px;
            text-align: center;
            opacity: 0.8;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 74, 173, 0.08);
            border-radius: 50%;
            padding: 0.5rem;
        }

        .client-details p:nth-child(even) i {
            color: var(--primary-pink);
            background-color: rgba(205, 32, 139, 0.08);
        }

        .detail-label {
            margin-right: 0.5rem;
            color: #777;
            font-weight: 400;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .client-details strong {
            font-weight: 600;
            color: #333;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .action-buttons {
            display: flex;
            gap: 1.2rem;
            flex-wrap: wrap;
            margin-top: 1.5rem;
        }

        .profile-action-btn {
            padding: 0.8rem 1.5rem;
            border-radius: 30px;
            border: none;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        .profile-action-btn i {
            margin-right: 0.6rem;
            font-size: 0.95rem;
            transition: transform 0.3s ease;
        }

        .profile-action-btn:hover i {
            transform: translateX(-3px);
        }

        .profile-action-btn.primary {
            background-color: var(--primary-blue);
            color: white;
            box-shadow: 0 4px 10px rgba(0,74,173,0.15);
        }

        .profile-action-btn.primary:hover {
            background-color: #003d91;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0,74,173,0.2);
        }

        .profile-action-btn.secondary {
            background-color: white;
            color: var(--primary-pink);
            border: 1px solid var(--primary-pink);
        }

        .profile-action-btn.secondary:hover {
            background-color: var(--primary-pink);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(205, 32, 139, 0.15);
        }

        .profile-action-btn:active {
            transform: translateY(1px);
            box-shadow: none;
        }

        /* Stats Section Styles */
        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.2rem;
            padding: 1.8rem 2rem;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            position: relative;
            background-color: #fafbfd;
        }

        .stats::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
            opacity: 0.8;
        }

        .stat {
            background-color: white;
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            border: 1px solid #e0e4e8;
            position: relative;
            overflow: hidden;
        }

        .stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 74, 173, 0.08), 0 10px 20px rgba(205, 32, 139, 0.05);
            border-color: rgba(0, 74, 173, 0.3);
        }

        .stat:hover::before {
            opacity: 1;
        }

        .stat:nth-child(odd) h3 {
            color: var(--primary-blue);
        }

        .stat:nth-child(even) h3 {
            color: var(--primary-pink);
        }

        .stat h3 {
            font-size: 2rem;
            margin-bottom: 0.8rem;
            font-weight: 700;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease;
            position: relative;
            display: inline-block;
        }

        .stat:hover h3 {
            transform: scale(1.1);
        }

        .stat p {
            color: #666;
            font-size: 0.95rem;
            margin: 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: color 0.3s ease;
            font-weight: 500;
        }

        .stat:hover p {
            color: #333;
        }

        @media (max-width: 992px) {
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 576px) {
            .stats {
                grid-template-columns: 1fr;
                padding: 1rem;
            }
        }

        /* Introduction Section Styles */
        .introduction {
            padding: 1.8rem 2rem;
            border-top: 1px solid #eee;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            position: relative;
            background-color: #fafbfd;
            border-radius: 0 0 16px 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.03);
            overflow: hidden;
        }

        .introduction::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
            opacity: 0.8;
        }

        .introduction h3 {
            font-size: 1.3rem;
            color: var(--primary-blue);
            margin-bottom: 1.2rem;
            font-weight: 600;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            position: relative;
            display: inline-block;
            padding-left: 1rem;
        }

        .introduction h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0.2rem;
            bottom: 0.2rem;
            width: 3px;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            border-radius: 3px;
        }

        .introduction p {
            line-height: 1.6;
            font-size: 0.95rem;
            position: relative;
            max-height: 100px;
            overflow: hidden;
            transition: max-height 0.5s ease, color 0.3s ease;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            padding: 0.5rem 1rem;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
            border: 1px solid rgba(0, 74, 173, 0.08);
        }

        .introduction p.expanded {
            max-height: 1000px;
        }

        .introduction p .intro-text {
            color: #555;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            font-weight: 400;
        }

        .introduction p .intro-text strong {
            color: var(--primary-pink);
            font-weight: 600;
        }

        .introduction p .no-intro-text {
            color: #999 !important;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            font-style: italic;
        }

        .read-more-btn {
            background: none;
            border: none;
            color: var(--primary-blue);
            font-weight: 500;
            cursor: pointer;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            margin-top: 0.8rem;
            display: inline-flex;
            align-items: center;
            gap: 0.4rem;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .read-more-btn::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            transition: transform 0.3s ease;
        }

        .read-more-btn.expanded::after {
            transform: rotate(180deg);
        }

        .read-more-btn:hover {
            color: var(--primary-pink);
            background-color: rgba(0, 74, 173, 0.05);
        }

        @media (max-width: 768px) {
            .introduction {
                padding: 1.2rem 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .introduction {
                padding: 1rem;
            }

            .introduction h3 {
                font-size: 1.2rem;
            }

            .introduction p {
                font-size: 0.9rem;
            }
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
            .nav-links {
                gap: 0.5rem;
            }

            .nav-links a {
                padding: 0.5rem 0.7rem;
                font-size: 1rem;
            }

            .nav-dropbtn {
                padding: 0.5rem 0.7rem;
                font-size: 1rem;
            }
        }

        @media (max-width: 992px) {
            .navbar-left {
                padding-left: 3rem; /* Space for the hamburger icon */
                position: relative;
            }

            .right-section {
                padding-right: 0.5rem;
            }

            .logo img {
                width: 3.2rem;
                height: 3.2rem;
            }

            .logo h1 {
                font-size: 1.4rem;
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: flex; /* Show in mobile view */
                position: absolute;
                left: 0.5rem;
                top: 50%;
                transform: translateY(-50%);
            }

            .nav-links.active {
                display: flex;
                flex-direction: column;
                position: absolute;
                top: 4.5rem;
                left: 0;
                right: 0;
                background: white;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                z-index: 1000;
                padding: 0.5rem 0;
                width: 100%;
                align-items: flex-start;
                margin: 0;
            }

            .nav-links.active a {
                width: 100%;
                padding: 0.7rem 1.2rem;
                margin: 0;
            }

            .nav-dropdown {
                width: 100%;
                margin: 0;
            }

            .nav-dropbtn {
                width: 100%;
                justify-content: space-between;
                padding: 0.7rem 1.2rem;
                margin: 0;
            }

            .nav-dropdown-content {
                position: static;
                box-shadow: none;
                width: 100%;
                padding-left: 1.2rem;
                display: none;
                margin: 0;
            }

            .nav-dropdown-content a {
                padding: 0.7rem 1.2rem;
                margin: 0;
            }

            .nav-dropdown.active .nav-dropdown-content {
                display: block;
            }
        }

        @media (max-width: 768px) {
            .welcome {
                flex-direction: column;
                align-items: center;
                text-align: center;
                padding: 0 1rem 1.5rem;
            }

            .profile-photo-container {
                margin-right: 0;
                margin-bottom: 1.5rem;
            }

            .info {
                padding-top: 0;
                width: 100%;
            }

            .name-badge {
                flex-direction: column;
                gap: 0.8rem;
            }

            .client-badge {
                margin-left: 0;
            }

            .client-details {
                grid-template-columns: 1fr;
                text-align: left;
                padding-left: 1rem;
            }

            .action-buttons {
                justify-content: center;
                margin-top: 1rem;
            }

            /* Draft section responsive styles */
            .content-wrapper {
                grid-template-columns: 1fr;
            }

            .draft-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
                padding: 1rem;
            }

            .draft-actions {
                width: 100%;
                justify-content: space-between;
            }

            .fill-draft-btn {
                flex: 1;
                max-width: none;
                padding: 0.7rem 1.2rem;
                font-size: 0.9rem;
                min-width: 0;
                border-width: 3px;
                animation: glow 1.5s infinite alternate;
                background: #004AAD; /* Maintain solid blue background */
                border-color: #CD208B; /* Maintain pink border */
            }

            .draft-info {
                width: 100%;
            }

            .draft-info .empty-icon {
                font-size: 1.5rem;
                padding: 0.6rem;
            }

            .text-group h4 {
                font-size: 1rem;
            }

            .no-drafts-message {
                padding: 1.5rem;
            }

            .no-drafts-message i {
                font-size: 2.5rem;
                padding: 1rem;
            }

            .no-drafts-message p {
                font-size: 1rem;
            }

            .right-section {
                width: 100%;
                justify-content: space-between;
                margin-top: 0.5rem;
            }

            .search-container {
                flex: 1;
                max-width: 300px;
            }

            .navbar {
                height: auto;
                min-height: 5.5rem;
                padding: 0.5rem;
                flex-wrap: wrap;
            }

            .navbar-left {
                width: 100%;
                justify-content: space-between;
            }
        }

        @media (max-width: 576px) {
            .search-type-button {
                padding: 0 0.5rem;
                font-size: 1rem;
            }

            .notification-dropdown {
                width: 300px;
                right: -100px;
            }

            .profile-dropdown-content {
                right: -50px;
            }

            .auth-buttons {
                gap: 1rem;
            }

            .profile-button {
                width: 40px;
                height: 40px;
            }

            .notification-icon i {
                font-size: 1.3rem;
            }

            .action-buttons {
                flex-direction: column;
                gap: 0.8rem;
                width: 100%;
                padding: 0 1rem;
            }

            .profile-action-btn {
                width: 100%;
                justify-content: center;
            }

            /* Draft section small screen styles */
            .draft-content::before {
                width: 3px;
            }

            .draft-info {
                gap: 0.7rem;
            }

            .draft-info .empty-icon {
                font-size: 1.2rem;
                padding: 0.5rem;
            }

            .text-group h4 {
                font-size: 0.95rem;
            }

            .text-group p {
                font-size: 0.8rem;
            }

            .draft-details {
                font-size: 0.75rem;
            }

            .fill-draft-btn {
                padding: 0.6rem 1rem;
                font-size: 0.8rem;
                letter-spacing: 0.3px;
                border-radius: 6px;
                border-width: 3px;
                font-weight: 700;
                animation: glow 1.5s infinite alternate;
                background: #004AAD; /* Maintain solid blue background */
                border-color: #CD208B; /* Maintain pink border */
                color: white; /* Ensure text is white */
            }

            .fill-draft-btn::after {
                font-size: 0.7rem;
            }

            @keyframes glow {
                0% {
                    box-shadow: 0 4px 10px rgba(0, 74, 173, 0.5);
                }
                50% {
                    box-shadow: 0 4px 15px rgba(205, 32, 139, 0.6);
                }
                100% {
                    box-shadow: 0 4px 10px rgba(0, 74, 173, 0.5);
                }
            }

            .no-drafts-message {
                padding: 1.2rem;
            }

            .no-drafts-message i {
                font-size: 2rem;
                padding: 0.8rem;
            }

            .no-drafts-message p {
                font-size: 0.9rem;
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 480px) {
            .welcome {
                padding: 0 1rem 1.5rem;
            }

            .profile-photo-container {
                margin-right: 1rem;
            }

            .profile-photo-container img {
                width: 80px;
                height: 80px;
            }

            .info {
                padding-top: 40px;
            }

            .name-badge h2 {
                font-size: 1.3rem;
            }

            .client-details {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .navbar-left {
                padding-left: 2.5rem; /* Adjusted spacing for smaller screens */
            }

            .mobile-menu-btn {
                left: 0.3rem; /* Position closer to edge on small screens */
            }

            .right-section {
                padding-right: 0.3rem;
                gap: 0.8rem;
                justify-content: flex-end;
            }

            .logo img {
                width: 3rem;
                height: 3rem;
            }

            .logo h1 {
                font-size: 1.2rem;
                margin-left: 0.3rem;
                margin-right: 0.3rem;
            }

            .search-container {
                max-width: 100%;
                margin-right: 0.5rem;
            }

            .search-bar {
                width: 120px;
            }

            .profile-banner {
                height: 120px;
            }

            .welcome {
                margin-top: -40px;
            }

            .profile-photo-container img {
                width: 70px;
                height: 70px;
                border-width: 3px;
            }

            .info {
                padding-top: 30px;
            }

            .client-badge {
                font-size: 0.7rem;
                padding: 0.2rem 0.6rem;
            }

            .client-details p {
                font-size: 0.85rem;
            }

            .profile-action-btn {
                padding: 0.6rem 1rem;
                font-size: 0.8rem;
            }
        }
        /* Improved Toast notification */
        .toast {
            position: fixed;
            bottom: 30px;
            right: 30px;
            padding: 18px 24px;
            border-radius: 16px;
            color: white;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            z-index: 1000;
            display: flex;
            align-items: center;
            box-shadow: 0 15px 40px rgba(0, 74, 173, 0.15), 0 10px 20px rgba(205, 32, 139, 0.1);
            max-width: 350px;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            border-left: 4px solid;
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.95), rgba(205, 32, 139, 0.95));
        }

        .toast.success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.95), rgba(0, 148, 133, 0.95));
            border-left-color: #10b981;
        }

        .toast.error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.95), rgba(220, 38, 38, 0.95));
            border-left-color: #ef4444;
        }

        .toast.warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.95), rgba(217, 119, 6, 0.95));
            border-left-color: #f59e0b;
        }

        .toast.info {
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.95), rgba(59, 130, 246, 0.95));
            border-left-color: var(--primary-blue);
        }

        .toast i {
            margin-right: 12px;
            font-size: 1.2rem;
        }

        .toast-content {
            flex: 1;
        }

        .toast-title {
            font-weight: 600;
            margin-bottom: 4px;
            font-size: 1rem;
        }

        .toast-message {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .toast-close {
            background: none;
            border: none;
            color: white;
            opacity: 0.7;
            cursor: pointer;
            font-size: 1.1rem;
            padding: 0;
            margin-left: 12px;
            transition: all 0.2s ease;
        }

        .toast-close:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        @media (max-width: 576px) {
            .toast {
                bottom: 20px;
                right: 20px;
                left: 20px;
                max-width: none;
                width: calc(100% - 40px);
                padding: 12px 16px;
                font-size: 0.9rem;
            }
        }

        .toast.show {
            opacity: 1;
            transform: translateY(0);
        }

        .toast.success {
            background-color: var(--success);
        }

        .toast.error {
            background-color: var(--error);
        }

        .toast i {
            margin-right: 12px;
            font-size: 1.2rem;
        }

        /* Custom scrollbar for notification dropdown */
        .notification-dropdown::-webkit-scrollbar {
            width: 6px;
        }

        .notification-dropdown::-webkit-scrollbar-track {
            background: transparent;
        }

        .notification-dropdown::-webkit-scrollbar-thumb {
            background-color: rgba(0,0,0,0.2);
            border-radius: 3px;
        }

        .notification-dropdown::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0,0,0,0.3);
        }
        /* View Contracts Button */
        .view-contracts-btn {
            background: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 30px;
            padding: 0.8rem 1.5rem;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            box-shadow: 0 4px 10px rgba(0,74,173,0.15);
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .view-contracts-btn:hover {
            background-color: #003d91;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0,74,173,0.2);
        }

        .paused-empty-container .empty-icon {
            color: var(--primary-pink);
        }

        /* Custom Select Dropdown Styles */
        .custom-select-wrapper {
            position: relative;
            width: 100%;
        }

        .custom-select-trigger {
            width: 100%;
            padding: 0.9rem 1rem;
            font-size: 0.95rem;
            border: 1px solid #e0e4e8;
            border-radius: 10px;
            background-color: white;
            color: #444;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            user-select: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
            min-height: 48px;
        }

        .custom-select-trigger:hover {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        .custom-select-trigger::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
            color: #666;
            font-size: 16px;
        }

        .custom-select-wrapper.open .custom-select-trigger {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        .custom-select-wrapper.open .custom-select-trigger::after {
            transform: translateY(-50%) rotate(-180deg);
            color: var(--primary-blue);
        }

        .custom-select-options {
            position: fixed;
            background-color: white;
            border: 1px solid #e0e4e8;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            max-height: 300px;
            overflow-y: auto;
            display: none;
            animation: dropdownFadeIn 0.2s ease-out;
            min-width: 200px;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .custom-select-wrapper.open .custom-select-options {
            display: block;
        }

        .custom-select-continent {
            padding: 0.8rem 1rem;
            font-weight: 600;
            color: var(--primary-blue);
            background-color: #f8f9fa;
            cursor: default;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .custom-select-option {
            padding: 0.8rem 1.5rem;
            font-size: 0.9rem;
            color: #444;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .custom-select-option:last-child {
            border-bottom: none;
        }

        .custom-select-option:hover {
            background-color: rgba(0, 74, 173, 0.08);
            color: var(--primary-blue);
            padding-left: 2rem;
        }

        .custom-select-option.selected {
            background-color: rgba(0, 74, 173, 0.1);
            color: var(--primary-blue);
            font-weight: 500;
            position: relative;
            padding-left: 2.5rem;
        }

        .custom-select-option.selected::before {
            content: '\f00c';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            left: 1rem;
            color: var(--primary-blue);
        }

        /* Custom scrollbar for dropdown */
        .custom-select-options::-webkit-scrollbar {
            width: 6px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: transparent;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background-color: rgba(0, 74, 173, 0.3);
            border-radius: 3px;
        }

        .custom-select-options::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0, 74, 173, 0.5);
        }

        /* Hide original select */
        #countrySelect.filter-select {
            display: none !important;
            visibility: hidden;
            position: absolute;
            left: -9999px;
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Main Content -->
    <div class="container">
        <!-- Navbar -->
        <nav class="navbar">
            <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                    <div class="logo">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                        <h1>GigGenius</h1>
                    </div>
                </a>
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="nav-links" id="navLinks">
                    <a href="{{ url_for('page1') }}">Post a Gig</a>

                    <!-- Overview Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Overview
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('allgigpost') }}">All gig posts</a>
                            <a href="{{ url_for('landing_page') }}">All contracts</a>
                            <a href="{{ url_for('landing_page') }}">Your Hires</a>
                        </div>
                    </div>

                    <!-- Manage Work Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Manage Work
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Timesheet</a>
                            <a href="{{ url_for('landing_page') }}">Invoices</a>
                        </div>
                    </div>

                    <!-- Reports Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Reports
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Weekly Summary</a>
                            <a href="{{ url_for('landing_page') }}">Transaction History</a>
                        </div>
                    </div>
                    <a href="{{ url_for('messages') }}">Messages</a>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Search for Geniuses">
                        <i class="fas fa-search icon"></i>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-icon">
                        <i class="fas fa-bell"></i>
                        <span id="notification-count">0</span>
                    </div>
                    <div class="notification-dropdown">
                        <div class="notification-header">
                            <span>Notifications</span>
                            <span class="notification-header-actions">Mark all as read</span>
                        </div>
                        <div id="notification-list">
                            <!-- Notifications will be loaded here -->
                        </div>
                        <div id="empty-notifications" class="empty-notifications" style="display: none;">
                            <i class="far fa-bell-slash"></i>
                            <p>No notifications yet</p>
                        </div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ url_for('api_profile_photo', user_type='client', user_id=session.get('user_id')) }}" alt="Profile Picture">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-cog"></i> Account Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Profile Section -->
        <div class="profile-section">
            <div class="profile-header">
                <div class="profile-banner">
                    <!-- Pattern decoration is now in CSS -->
                </div>
                <div class="welcome">
                    <div class="profile-photo-container">
                        <img src="{{ url_for('api_profile_photo', user_type='client', user_id=session.get('user_id')) }}" alt="Profile Picture">
                    </div>
                    <div class="info">
                        <div class="name-badge">
                            <h2>{{ session.first_name }} {{ session.last_name }}</h2>
                            <div class="client-badge">Premium Client</div>
                        </div>
                        <div class="client-details">
                            <p>
                                <i class="fas fa-building"></i>
                                <span class="detail-label">Company:</span>
                                <strong>{{ session.company_name }}</strong>
                            </p>
                            <p>
                                <i class="fas fa-map-marker-alt"></i>
                                <span class="detail-label">Country:</span>
                                <strong>{{ client.country }}</strong>
                            </p>
                            <p>
                                <i class="fas fa-briefcase"></i>
                                <span class="detail-label">Position:</span>
                                <strong>{{ session.position }}</strong>
                            </p>
                            <p>
                                <i class="fas fa-clock"></i>
                                <span class="detail-label">Member since:</span>
                                <strong>June 2023</strong>
                            </p>
                        </div>
                        <div class="action-buttons">
                            <button class="profile-action-btn primary">
                                <i class="fas fa-edit"></i> Edit Profile
                            </button>
                            <button class="profile-action-btn secondary">
                                <i class="fas fa-cog"></i> Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="stats">
                <div class="stat">
                    <h3>{{ stats.total_hired }}</h3>
                    <p>Total Hired</p>
                </div>
                <div class="stat">
                    <h3>${{ "%.2f"|format(stats.total_spent) }}</h3>
                    <p>Total Spent</p>
                </div>
                <div class="stat">
                    <h3>{{ stats.posted_jobs }}</h3>
                    <p>Posted Jobs</p>
                </div>
                <div class="stat">
                    <h3>{{ stats.total_applications }}</h3>
                    <p>Applications</p>
                </div>
            </div>

            <!-- Introduction Section -->
            <div class="introduction">
                <h3>Introduction</h3>
                <p id="introduction-text">
                    {% if client and client.introduction %}
                        <span class="intro-text">{{ client.introduction }}</span>
                    {% else %}
                        <span class="no-intro-text">No introduction available. Please add an introduction in your profile settings.</span>
                    {% endif %}
                </p>
                <button onclick="toggleIntroduction()" class="read-more-btn" id="read-more-btn">Read More</button>
            </div>

            <!-- Quote Slider -->
            <header class="quote-section">
                <div class="quote-slider">
                    <div class="quote-navigation">
                        <span class="arrow left" onclick="changeQuote(-1)">&larr;</span>
                        <h1 class="quote" id="currentQuote"></h1>
                        <span class="arrow right" onclick="changeQuote(1)">&rarr;</span>
                    </div>
                </div>
            </header>

            <!-- Overview Section -->
            <div class="overview-section">
                <div class="overview-header">
                    <h2>Overview</h2>
                    <div class="header-right">
                        <div class="priority-list-container">
                            <div class="priority-list-header" onclick="togglePriorityDropdown()">
                                <span class="priority-icon">⚑</span>
                                <h3>Priority List</h3>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </div>
                            <div class="priority-dropdown" id="priorityDropdown">
                                <div class="dropdown-item">Priority First</div>
                                <div class="dropdown-item">Newest First</div>
                                <div class="dropdown-item">Oldest First</div>
                            </div>
                        </div>
                        <div class="view-options">
                            <div class="view-toggle">
                                <button class="view-btn grid active" data-tooltip="View as Grid">
                                    <i class="fas fa-th-large"></i>
                                </button>
                                <button class="view-btn list" data-tooltip="View as List">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="overview-cards">
                    <div class="overview-card" onclick="toggleEmptyState('jobs')">
                        <div class="card-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="card-content">
                            <h3>Open job posts</h3>
                            <p>{{ jobs|selectattr('status', 'undefined')|list|length }}</p>
                        </div>
                    </div>

                    <div class="overview-card" onclick="toggleEmptyState('contracts')">
                        <div class="card-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <div class="card-content">
                            <h3>Active contracts</h3>
                            <p>0</p>
                        </div>
                    </div>

                    <div class="overview-card" onclick="toggleEmptyState('paused')">
                        <div class="card-icon">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                        <div class="card-content">
                            <h3>Paused contracts</h3>
                            <p>0</p>
                        </div>
                    </div>

                    <div class="overview-card" onclick="toggleEmptyState('drafts')">
                        <div class="card-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="card-content">
                            <h3>Draft job posts</h3>
                            <p>{{ draft_jobs|length }}</p>
                        </div>
                    </div>
                </div>

                <div class="empty-state jobs" style="display: none;">
                    <div class="jobs-empty-container">
                        {% if jobs|selectattr('status', 'undefined')|list|length > 0 %}
                            <div class="jobs-scroll-container">
                                {% for job in jobs if not job.status %}
                                <div class="job-content">
                                    <div class="job-info">
                                        <i class="fas fa-briefcase empty-icon"></i>
                                        <div class="job-text">
                                            <div class="text-group">
                                                <h4>{{ job.title }}</h4>
                                                <p>Open job post</p>
                                                <span class="job-details">{{ job.category }} • {{ job.budget_type|capitalize }} • {{ job.created_at_formatted }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-actions">
                                        <a href="{{ url_for('view_job', job_id=job.id) }}" class="view-job-btn">View Details</a>
                                        <button class="three-dots-btn">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="no-jobs-message">
                                <i class="fas fa-briefcase empty-icon"></i>
                                <p>No open job posts right now</p>
                                <a href="{{ url_for('page1') }}" class="post-job-btn">
                                    <i class="fas fa-plus"></i> Post a job
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="empty-state contracts" style="display: none;">
                    <div class="contracts-empty-container">
                        <i class="fas fa-file-contract empty-icon"></i>
                        <p>No active contracts right now</p>
                        <a href="{{ url_for('landing_page') }}" class="view-contracts-btn">
                            <i class="fas fa-eye"></i> View All Contracts
                        </a>
                    </div>
                </div>

                <div class="empty-state paused" style="display: none;">
                    <div class="paused-empty-container">
                        <i class="fas fa-pause-circle empty-icon"></i>
                        <p>No paused contracts right now</p>
                        <a href="{{ url_for('landing_page') }}" class="view-contracts-btn">
                            <i class="fas fa-eye"></i> View All Contracts
                        </a>
                    </div>
                </div>

                <div class="empty-state drafts" style="display: none;">
                    <div class="drafts-empty-container">
                        <div class="content-wrapper">
                            {% if draft_jobs %}
                                <div class="drafts-scroll-container">
                                    {% for draft in draft_jobs %}
                                    <div class="draft-content">
                                        <div class="draft-info">
                                            <i class="fas fa-file-alt empty-icon"></i>
                                            <div class="draft-text">
                                                <div class="text-group">
                                                    <h4>{{ draft.title }}</h4>
                                                    <p>Draft job post</p>
                                                    {% if not draft.description %}
                                                    <span class="draft-details">Add details to your draft</span>
                                                    {% elif not draft.skills %}
                                                    <span class="draft-details">Add skills to your draft</span>
                                                    {% else %}
                                                    <span class="draft-details">Continue editing your draft</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="draft-actions">
                                            <a href="{{ url_for('page3', draft_job_id=draft.id) }}" class="fill-draft-btn">Continue</a>
                                            <button class="three-dots-btn">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="no-drafts-message">
                                    <i class="fas fa-file-alt empty-icon"></i>
                                    <p>You don't have any draft job posts yet</p>
                                    <a href="{{ url_for('page1') }}" class="post-job-btn">
                                        <i class="fas fa-plus"></i> Create a job post
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>


            </div>

            <div class="content content-container">
                <main style="width: 100%;">
                    <div class="genius-header">
                        <h2>Find Talented Geniuses</h2>
                        <p class="genius-subtitle">Discover and connect with skilled professionals for your projects.</p>
                    </div>

                    <div class="search-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text"
                               class="search"
                               id="geniusSearch"
                               placeholder="Search geniuses by name, position, or skills..."
                               onkeyup="searchGenius()">
                    </div>

                    <!-- Horizontal Filter Section -->
                    <div class="horizontal-filters" id="filtersSection">
                        <div class="filter-header-row">
                            <div class="filter-header">
                                <i class="fas fa-filter"></i>
                                <h3>Filters</h3>
                            </div>
                            <button class="reset-filters-btn" id="resetFiltersBtn">
                                <i class="fas fa-redo"></i> Reset
                            </button>
                        </div>

                        <div class="filter-row">
                            <div class="filter-group">
                                <label class="filter-label">
                                    <i class="fas fa-map-marker-alt"></i> Country
                                </label>
                                <div class="custom-select-wrapper">
                                    <div class="custom-select-trigger">All Countries</div>
                                    <div class="custom-select-options">
                                        <!-- Options will be populated by JavaScript -->
                                    </div>
                                    <select id="countrySelect" class="filter-select" style="display: none;">
                                        <option value="">All Countries</option>
                                        <optgroup label="Africa">
                                            <option value="Algeria">Algeria</option>
                                            <option value="Angola">Angola</option>
                                            <option value="Benin">Benin</option>
                                            <option value="Botswana">Botswana</option>
                                            <option value="Burkina Faso">Burkina Faso</option>
                                            <option value="Burundi">Burundi</option>
                                            <option value="Cabo Verde">Cabo Verde</option>
                                            <option value="Cameroon">Cameroon</option>
                                            <option value="Central African Republic">Central African Republic</option>
                                            <option value="Chad">Chad</option>
                                            <option value="Comoros">Comoros</option>
                                            <option value="Congo, Democratic Republic of the">Congo, Democratic Republic of the</option>
                                            <option value="Congo, Republic of the">Congo, Republic of the</option>
                                            <option value="Djibouti">Djibouti</option>
                                            <option value="Egypt">Egypt</option>
                                            <option value="Equatorial Guinea">Equatorial Guinea</option>
                                            <option value="Eritrea">Eritrea</option>
                                            <option value="Eswatini">Eswatini</option>
                                            <option value="Ethiopia">Ethiopia</option>
                                            <option value="Gabon">Gabon</option>
                                            <option value="Gambia">Gambia</option>
                                            <option value="Ghana">Ghana</option>
                                            <option value="Guinea">Guinea</option>
                                            <option value="Guinea-Bissau">Guinea-Bissau</option>
                                            <option value="Kenya">Kenya</option>
                                            <option value="Lesotho">Lesotho</option>
                                            <option value="Liberia">Liberia</option>
                                            <option value="Libya">Libya</option>
                                            <option value="Madagascar">Madagascar</option>
                                            <option value="Malawi">Malawi</option>
                                            <option value="Mali">Mali</option>
                                            <option value="Mauritania">Mauritania</option>
                                            <option value="Mauritius">Mauritius</option>
                                            <option value="Morocco">Morocco</option>
                                            <option value="Mozambique">Mozambique</option>
                                            <option value="Namibia">Namibia</option>
                                            <option value="Niger">Niger</option>
                                            <option value="Nigeria">Nigeria</option>
                                            <option value="Rwanda">Rwanda</option>
                                            <option value="Sao Tome and Principe">Sao Tome and Principe</option>
                                            <option value="Senegal">Senegal</option>
                                            <option value="Seychelles">Seychelles</option>
                                            <option value="Sierra Leone">Sierra Leone</option>
                                            <option value="Somalia">Somalia</option>
                                            <option value="South Africa">South Africa</option>
                                            <option value="South Sudan">South Sudan</option>
                                            <option value="Sudan">Sudan</option>
                                            <option value="Tanzania">Tanzania</option>
                                            <option value="Togo">Togo</option>
                                            <option value="Tunisia">Tunisia</option>
                                            <option value="Uganda">Uganda</option>
                                            <option value="Zambia">Zambia</option>
                                            <option value="Zimbabwe">Zimbabwe</option>
                                        </optgroup>
                                        <optgroup label="Asia">
                                            <option value="Afghanistan">Afghanistan</option>
                                            <option value="Armenia">Armenia</option>
                                            <option value="Azerbaijan">Azerbaijan</option>
                                            <option value="Bahrain">Bahrain</option>
                                            <option value="Bangladesh">Bangladesh</option>
                                            <option value="Bhutan">Bhutan</option>
                                            <option value="Brunei">Brunei</option>
                                            <option value="Cambodia">Cambodia</option>
                                            <option value="China">China</option>
                                            <option value="Cyprus">Cyprus</option>
                                            <option value="Georgia">Georgia</option>
                                            <option value="India">India</option>
                                            <option value="Indonesia">Indonesia</option>
                                            <option value="Iran">Iran</option>
                                            <option value="Iraq">Iraq</option>
                                            <option value="Israel">Israel</option>
                                            <option value="Japan">Japan</option>
                                            <option value="Jordan">Jordan</option>
                                            <option value="Kazakhstan">Kazakhstan</option>
                                            <option value="Kuwait">Kuwait</option>
                                            <option value="Kyrgyzstan">Kyrgyzstan</option>
                                            <option value="Laos">Laos</option>
                                            <option value="Lebanon">Lebanon</option>
                                            <option value="Malaysia">Malaysia</option>
                                            <option value="Maldives">Maldives</option>
                                            <option value="Mongolia">Mongolia</option>
                                            <option value="Myanmar (Burma)">Myanmar (Burma)</option>
                                            <option value="Nepal">Nepal</option>
                                            <option value="North Korea">North Korea</option>
                                            <option value="Oman">Oman</option>
                                            <option value="Pakistan">Pakistan</option>
                                            <option value="Palestine State">Palestine State</option>
                                            <option value="Philippines">Philippines</option>
                                            <option value="Qatar">Qatar</option>
                                            <option value="Saudi Arabia">Saudi Arabia</option>
                                            <option value="Singapore">Singapore</option>
                                            <option value="South Korea">South Korea</option>
                                            <option value="Sri Lanka">Sri Lanka</option>
                                            <option value="Syria">Syria</option>
                                            <option value="Taiwan">Taiwan</option>
                                            <option value="Tajikistan">Tajikistan</option>
                                            <option value="Thailand">Thailand</option>
                                            <option value="Timor-Leste">Timor-Leste</option>
                                            <option value="Turkey">Turkey</option>
                                            <option value="Turkmenistan">Turkmenistan</option>
                                            <option value="United Arab Emirates">United Arab Emirates</option>
                                            <option value="Uzbekistan">Uzbekistan</option>
                                            <option value="Vietnam">Vietnam</option>
                                            <option value="Yemen">Yemen</option>
                                        </optgroup>
                                        <optgroup label="Europe">
                                            <option value="Albania">Albania</option>
                                            <option value="Andorra">Andorra</option>
                                            <option value="Austria">Austria</option>
                                            <option value="Belarus">Belarus</option>
                                            <option value="Belgium">Belgium</option>
                                            <option value="Bosnia and Herzegovina">Bosnia and Herzegovina</option>
                                            <option value="Bulgaria">Bulgaria</option>
                                            <option value="Croatia">Croatia</option>
                                            <option value="Czech Republic">Czech Republic</option>
                                            <option value="Denmark">Denmark</option>
                                            <option value="Estonia">Estonia</option>
                                            <option value="Finland">Finland</option>
                                            <option value="France">France</option>
                                            <option value="Germany">Germany</option>
                                            <option value="Greece">Greece</option>
                                            <option value="Hungary">Hungary</option>
                                            <option value="Iceland">Iceland</option>
                                            <option value="Ireland">Ireland</option>
                                            <option value="Italy">Italy</option>
                                            <option value="Latvia">Latvia</option>
                                            <option value="Liechtenstein">Liechtenstein</option>
                                            <option value="Lithuania">Lithuania</option>
                                            <option value="Luxembourg">Luxembourg</option>
                                            <option value="Malta">Malta</option>
                                            <option value="Moldova">Moldova</option>
                                            <option value="Monaco">Monaco</option>
                                            <option value="Montenegro">Montenegro</option>
                                            <option value="Netherlands">Netherlands</option>
                                            <option value="North Macedonia">North Macedonia</option>
                                            <option value="Norway">Norway</option>
                                            <option value="Poland">Poland</option>
                                            <option value="Portugal">Portugal</option>
                                            <option value="Romania">Romania</option>
                                            <option value="Russia">Russia</option>
                                            <option value="San Marino">San Marino</option>
                                            <option value="Serbia">Serbia</option>
                                            <option value="Slovakia">Slovakia</option>
                                            <option value="Slovenia">Slovenia</option>
                                            <option value="Spain">Spain</option>
                                            <option value="Sweden">Sweden</option>
                                            <option value="Switzerland">Switzerland</option>
                                            <option value="Ukraine">Ukraine</option>
                                            <option value="United Kingdom">United Kingdom</option>
                                            <option value="Vatican City">Vatican City</option>
                                        </optgroup>
                                        <optgroup label="North America">
                                            <option value="Antigua and Barbuda">Antigua and Barbuda</option>
                                            <option value="Bahamas">Bahamas</option>
                                            <option value="Barbados">Barbados</option>
                                            <option value="Belize">Belize</option>
                                            <option value="Canada">Canada</option>
                                            <option value="Costa Rica">Costa Rica</option>
                                            <option value="Cuba">Cuba</option>
                                            <option value="Dominica">Dominica</option>
                                            <option value="Dominican Republic">Dominican Republic</option>
                                            <option value="El Salvador">El Salvador</option>
                                            <option value="Grenada">Grenada</option>
                                            <option value="Guatemala">Guatemala</option>
                                            <option value="Haiti">Haiti</option>
                                            <option value="Honduras">Honduras</option>
                                            <option value="Jamaica">Jamaica</option>
                                            <option value="Mexico">Mexico</option>
                                            <option value="Nicaragua">Nicaragua</option>
                                            <option value="Panama">Panama</option>
                                            <option value="Saint Kitts and Nevis">Saint Kitts and Nevis</option>
                                            <option value="Saint Lucia">Saint Lucia</option>
                                            <option value="Saint Vincent and the Grenadines">Saint Vincent and the Grenadines</option>
                                            <option value="Trinidad and Tobago">Trinidad and Tobago</option>
                                            <option value="United States of America">United States of America</option>
                                        </optgroup>
                                        <optgroup label="Oceania">
                                            <option value="Australia">Australia</option>
                                            <option value="Fiji">Fiji</option>
                                            <option value="Kiribati">Kiribati</option>
                                            <option value="Marshall Islands">Marshall Islands</option>
                                            <option value="Micronesia">Micronesia</option>
                                            <option value="Nauru">Nauru</option>
                                            <option value="New Zealand">New Zealand</option>
                                            <option value="Palau">Palau</option>
                                            <option value="Papua New Guinea">Papua New Guinea</option>
                                            <option value="Samoa">Samoa</option>
                                            <option value="Solomon Islands">Solomon Islands</option>
                                            <option value="Tonga">Tonga</option>
                                            <option value="Tuvalu">Tuvalu</option>
                                            <option value="Vanuatu">Vanuatu</option>
                                        </optgroup>
                                        <optgroup label="South America">
                                            <option value="Argentina">Argentina</option>
                                            <option value="Bolivia">Bolivia</option>
                                            <option value="Brazil">Brazil</option>
                                            <option value="Chile">Chile</option>
                                            <option value="Colombia">Colombia</option>
                                            <option value="Ecuador">Ecuador</option>
                                            <option value="Guyana">Guyana</option>
                                            <option value="Paraguay">Paraguay</option>
                                            <option value="Peru">Peru</option>
                                            <option value="Suriname">Suriname</option>
                                            <option value="Uruguay">Uruguay</option>
                                            <option value="Venezuela">Venezuela</option>
                                        </optgroup>
                                    </select>
                                </div>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">
                                    <i class="fas fa-star"></i> Expertise
                                </label>
                                <div class="custom-select-wrapper">
                                    <select id="expertiseSelect" class="filter-select" style="display: none;">
                                        <option value="">All Levels</option>
                                        <option value="Entry">Entry Level</option>
                                        <option value="Intermediate">Intermediate</option>
                                        <option value="Expert">Expert</option>
                                    </select>
                                    <div class="custom-select-trigger">
                                        All Levels
                                    </div>
                                    <div class="custom-select-options">
                                        <!-- Options will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">
                                    <i class="fas fa-clock"></i> Availability
                                </label>
                                <div class="custom-select-wrapper">
                                    <select id="availabilitySelect" class="filter-select" style="display: none;">
                                        <option value="">Any Availability</option>
                                        <option value="Full-time">Full-time</option>
                                        <option value="Part-time">Part-time</option>
                                        <option value="Hourly">Hourly</option>
                                        <option value="As needed">As needed</option>
                                    </select>
                                    <div class="custom-select-trigger">
                                        Any Availability
                                    </div>
                                    <div class="custom-select-options">
                                        <!-- Options will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">
                                    <i class="fas fa-dollar-sign"></i> Hourly Rate
                                </label>
                                <div class="price-range">
                                    <input type="number"
                                           id="hourlyRateMin"
                                           name="hourlyRateMin"
                                           min="0"
                                           max="1000"
                                           step="5"
                                           placeholder="Min ($)"
                                           class="price-input">
                                    <span class="price-separator">-</span>
                                    <input type="number"
                                           id="hourlyRateMax"
                                           name="hourlyRateMax"
                                           min="0"
                                           max="1000"
                                           step="5"
                                           placeholder="Max ($)"
                                           class="price-input">
                                </div>
                            </div>



                            <div class="filter-group post-job-container">
                                <a href="{{ url_for('page1') }}" class="post-job-btn">
                                    <i class="fas fa-plus"></i> Post Job
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="genius-container">
                        <div class="genius-grid" id="geniusGrid">
                            <!-- Genius profiles will be loaded here via JavaScript -->
                            <div class="loading-message" id="loadingMessage">
                                <i class="fas fa-spinner fa-spin"></i>
                                <h3>Loading genius profiles...</h3>
                            </div>

                            <div class="no-genius-message" id="noGeniusMessage" style="display: none;">
                                <i class="fas fa-user-graduate"></i>
                                <h3>No genius profiles found</h3>
                                <p>Try adjusting your filters or search criteria.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Genius Profile Template (Hidden) -->
                    <template id="geniusCardTemplate">
                        <div class="genius-card">
                            <div class="expert-badge">EXPERT</div>
                            <div class="genius-image">
                                <img src="" alt="Genius Profile">
                            </div>
                            <div class="genius-header">
                                <h3 class="genius-name"></h3>
                                <div class="genius-description">
                                    <p class="introduction-text"></p>
                                    <button class="see-more-btn">
                                        See More <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="genius-info">
                                <div class="genius-details">
                                    <div class="detail-item">
                                        <i class="fas fa-briefcase"></i>
                                        <span class="detail-label">Position:</span>
                                        <span class="detail-value position-value"></span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span class="detail-label">Country:</span>
                                        <span class="detail-value country-value"></span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span class="detail-label">Hourly Rate:</span>
                                        <span class="detail-value rate-value"></span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-star"></i>
                                        <span class="detail-label">Expertise:</span>
                                        <span class="detail-value expertise-value"></span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-clock"></i>
                                        <span class="detail-label">Availability:</span>
                                        <span class="detail-value availability-value"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="genius-actions">
                                <a href="#" class="view-profile-btn">
                                    <i class="fas fa-eye"></i> View Profile
                                </a>
                                <a href="#" class="contact-genius-btn">
                                    <i class="fas fa-envelope"></i> Contact
                                </a>
                            </div>
                        </div>
                    </template>
                </main>
            </div>
        </div>
    </div>

    <script>
        // Quote slider functionality
        const quotes = [
            "Connect with top talent for your projects",
            "Find the perfect genius for your next big idea",
            "Build your dream team with GigGenius",
            "Quality work delivered by verified professionals",
            "Streamline your hiring process with our platform",
            "Access specialized skills on demand",
            "Hire the best minds for your business challenges",
            "Transform your ideas into reality with expert help"
        ];

        let currentQuoteIndex = 0;
        let isAnimating = false;

        function updateQuote(newIndex) {
            if (isAnimating) return;
            isAnimating = true;

            const quoteElement = document.getElementById('currentQuote');

            // Fade out
            quoteElement.style.opacity = '0';
            quoteElement.style.transform = 'translateY(10px)';

            setTimeout(() => {
                // Update content
                quoteElement.textContent = quotes[newIndex];

                // Fade in
                setTimeout(() => {
                    quoteElement.style.opacity = '1';
                    quoteElement.style.transform = 'translateY(0)';
                    isAnimating = false;
                }, 50);
            }, 300);
        }

        function changeQuote(direction) {
            if (isAnimating) return;
            const newIndex = (currentQuoteIndex + direction + quotes.length) % quotes.length;
            currentQuoteIndex = newIndex;
            updateQuote(newIndex);
        }

        // Function to toggle the introduction text
        function toggleIntroduction() {
            const introText = document.getElementById('introduction-text');
            const button = document.getElementById('read-more-btn');

            if (introText.classList.contains('expanded')) {
                introText.classList.remove('expanded');
                button.textContent = 'Read More';
            } else {
                introText.classList.add('expanded');
                button.textContent = 'Read Less';
            }
        }

        function togglePriorityDropdown() {
            const dropdown = document.getElementById('priorityDropdown');
            const arrow = document.querySelector('.dropdown-arrow');

            dropdown.classList.toggle('active');

            if (dropdown.classList.contains('active')) {
                arrow.style.transform = 'rotate(180deg)';
            } else {
                arrow.style.transform = 'rotate(0)';
            }

            // Close dropdown when clicking outside
            document.addEventListener('click', function closeDropdown(e) {
                const priorityContainer = document.querySelector('.priority-list-container');
                if (!priorityContainer.contains(e.target)) {
                    dropdown.classList.remove('active');
                    arrow.style.transform = 'rotate(0)';
                    document.removeEventListener('click', closeDropdown);
                }
            });
        }

        function toggleEmptyState(type) {
            const selectedState = document.querySelector(`.empty-state.${type}`);
            const clickedCard = document.querySelector(`.overview-card[onclick="toggleEmptyState('${type}')"]`);

            // Check if the clicked card is already active
            const isCurrentlyActive = clickedCard && clickedCard.classList.contains('active');

            if (isCurrentlyActive) {
                // If already active, hide the section and remove active class
                if (selectedState) {
                    selectedState.style.display = 'none';
                }
                clickedCard.classList.remove('active');
                console.log(`Hidden ${type} section`);
            } else {
                // Hide all empty states first
                const emptyStates = document.querySelectorAll('.empty-state');
                emptyStates.forEach(state => {
                    state.style.display = 'none';
                });

                // Remove active class from all cards
                const overviewCards = document.querySelectorAll('.overview-card');
                overviewCards.forEach(card => {
                    card.classList.remove('active');
                });

                // Show the selected empty state
                if (selectedState) {
                    selectedState.style.display = 'block';

                    // If this is the drafts section, make sure the draft content is visible
                    if (type === 'drafts') {
                        const draftContents = document.querySelectorAll('.draft-content');
                        draftContents.forEach(content => {
                            content.style.display = 'flex';
                        });

                        // Make sure the buttons are visible
                        const draftButtons = document.querySelectorAll('.fill-draft-btn');
                        draftButtons.forEach(button => {
                            button.style.display = 'flex';
                        });
                    }
                }

                // Add active class to clicked card
                if (clickedCard) {
                    clickedCard.classList.add('active');
                }

                console.log(`Shown ${type} section`);
            }
        }

        function searchGenius() {
            const input = document.getElementById('geniusSearch');
            const filter = input.value.toUpperCase();
            const grid = document.getElementById('geniusGrid');
            const cards = grid.getElementsByClassName('genius-card');
            let visibleCount = 0;

            for (let i = 0; i < cards.length; i++) {
                const card = cards[i];
                const name = card.querySelector('h3');
                const position = card.querySelector('.position-value');
                const country = card.querySelector('.country-value');
                const expertise = card.querySelector('.expertise-value');
                const intro = card.querySelector('.introduction-text');

                const nameValue = name ? (name.textContent || name.innerText) : '';
                const positionValue = position ? (position.textContent || position.innerText) : '';
                const countryValue = country ? (country.textContent || country.innerText) : '';
                const expertiseValue = expertise ? (expertise.textContent || expertise.innerText) : '';
                const introValue = intro ? (intro.textContent || intro.innerText) : '';

                if (nameValue.toUpperCase().indexOf(filter) > -1 ||
                    countryValue.toUpperCase().indexOf(filter) > -1 ||
                    expertiseValue.toUpperCase().indexOf(filter) > -1 ||
                    introValue.toUpperCase().indexOf(filter) > -1) {
                    card.style.display = '';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            }

            // Show or hide the no results message
            const noGeniusMessage = document.getElementById('noGeniusMessage');
            if (visibleCount === 0 && filter !== '') {
                noGeniusMessage.style.display = 'block';
                noGeniusMessage.querySelector('h3').textContent = 'No matching genius profiles';
                noGeniusMessage.querySelector('p').textContent = 'Try different search terms or adjust your filters.';
            } else {
                noGeniusMessage.style.display = 'none';
            }
        }

        function searchJobs() {
            const input = document.getElementById('jobSearch');
            const filter = input.value.toUpperCase();
            const grid = document.getElementById('geniusGrid');
            const cards = grid.getElementsByClassName('job-card');

            for (let i = 0; i < cards.length; i++) {
                const card = cards[i];
                const title = card.querySelector('h3');
                const category = card.querySelector('.detail-item:first-child .detail-value');
                const description = card.querySelector('.job-description p');

                const titleValue = title.textContent || title.innerText;
                const categoryValue = category ? (category.textContent || category.innerText) : '';
                const descriptionValue = description ? (description.textContent || description.innerText) : '';

                if (titleValue.toUpperCase().indexOf(filter) > -1 ||
                    categoryValue.toUpperCase().indexOf(filter) > -1 ||
                    descriptionValue.toUpperCase().indexOf(filter) > -1) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            }

            // Apply other filters
            applyJobFilters();
        }

        function applyJobFilters() {
            const jobTypeFilter = document.getElementById('jobTypeSelect').value.toUpperCase();
            const categoryFilter = document.getElementById('categorySelect').value.toUpperCase();
            const applicationsFilter = document.getElementById('applicationsSelect').value;
            const dateFilter = document.getElementById('datePostedSelect').value;
            const budgetFilter = document.getElementById('budget').value;

            const grid = document.getElementById('geniusGrid');
            const cards = grid.getElementsByClassName('job-card');

            for (let i = 0; i < cards.length; i++) {
                const card = cards[i];

                // Skip cards that are already hidden by the search
                if (card.style.display === 'none') continue;

                // Get job type
                const jobBadge = card.querySelector('.job-badge');
                const jobType = jobBadge ? jobBadge.textContent.toUpperCase() : '';

                // Get category
                const categoryElement = card.querySelector('.detail-item:first-child .detail-value');
                const category = categoryElement ? categoryElement.textContent.toUpperCase() : '';

                // Get applications count
                const applicationsElement = card.querySelector('.detail-item:nth-child(6) .detail-value');
                const applicationsCount = applicationsElement ? parseInt(applicationsElement.textContent) : 0;

                // Get date posted
                const dateElement = card.querySelector('.detail-item:nth-child(5) .detail-value');
                const datePosted = dateElement ? dateElement.textContent : '';

                // Get budget
                const budgetElement = card.querySelector('.detail-item:nth-child(4) .detail-value');
                const budgetText = budgetElement ? budgetElement.textContent : '';
                const budgetMatch = budgetText.match(/\$(\d+)/);
                const budget = budgetMatch ? parseInt(budgetMatch[1]) : 0;

                // Apply job type filter
                if (jobTypeFilter && !jobType.includes(jobTypeFilter)) {
                    card.style.display = 'none';
                    continue;
                }

                // Apply category filter
                if (categoryFilter && !category.includes(categoryFilter)) {
                    card.style.display = 'none';
                    continue;
                }

                // Apply applications filter
                if (applicationsFilter) {
                    if (applicationsFilter === '0' && applicationsCount !== 0) {
                        card.style.display = 'none';
                        continue;
                    } else if (applicationsFilter === '1-5' && (applicationsCount < 1 || applicationsCount > 5)) {
                        card.style.display = 'none';
                        continue;
                    } else if (applicationsFilter === '6-10' && (applicationsCount < 6 || applicationsCount > 10)) {
                        card.style.display = 'none';
                        continue;
                    } else if (applicationsFilter === '10+' && applicationsCount <= 10) {
                        card.style.display = 'none';
                        continue;
                    }
                }

                // Apply date filter
                if (dateFilter) {
                    const now = new Date();
                    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    const weekAgo = new Date(today);
                    weekAgo.setDate(today.getDate() - 7);
                    const monthAgo = new Date(today);
                    monthAgo.setMonth(today.getMonth() - 1);
                    const threeMonthsAgo = new Date(today);
                    threeMonthsAgo.setMonth(today.getMonth() - 3);

                    // Parse the date from the format "Month Day, Year"
                    const dateParts = datePosted.split(' ');
                    if (dateParts.length >= 3) {
                        const month = dateParts[0];
                        const day = parseInt(dateParts[1].replace(',', ''));
                        const year = parseInt(dateParts[2]);
                        const postDate = new Date(year, getMonthNumber(month), day);

                        if (dateFilter === 'today' && postDate < today) {
                            card.style.display = 'none';
                            continue;
                        } else if (dateFilter === 'week' && postDate < weekAgo) {
                            card.style.display = 'none';
                            continue;
                        } else if (dateFilter === 'month' && postDate < monthAgo) {
                            card.style.display = 'none';
                            continue;
                        } else if (dateFilter === '3months' && postDate < threeMonthsAgo) {
                            card.style.display = 'none';
                            continue;
                        }
                    }
                }

                // Apply budget filter
                if (budgetFilter && budget < parseInt(budgetFilter)) {
                    card.style.display = 'none';
                    continue;
                }
            }
        }

        function getMonthNumber(monthName) {
            const months = {
                'January': 0, 'February': 1, 'March': 2, 'April': 3, 'May': 4, 'June': 5,
                'July': 6, 'August': 7, 'September': 8, 'October': 9, 'November': 10, 'December': 11
            };
            return months[monthName] || 0;
        }

        // Function to fetch genius profiles from the API
        function fetchGeniusProfiles() {
            // Show loading message
            document.getElementById('loadingMessage').style.display = 'block';
            document.getElementById('noGeniusMessage').style.display = 'none';

            // Get filter values
            const country = document.getElementById('countrySelect').value;
            const expertise = document.getElementById('expertiseSelect').value;
            const availability = document.getElementById('availabilitySelect').value;
            const hourlyRateMin = document.getElementById('hourlyRateMin').value || 0;
            const hourlyRateMax = document.getElementById('hourlyRateMax').value || 1000;

            // Build query parameters
            const params = new URLSearchParams();
            if (country) params.append('country', country);
            if (expertise) params.append('expertise', expertise);
            if (availability) params.append('availability', availability);
            params.append('hourly_rate_min', hourlyRateMin);
            params.append('hourly_rate_max', hourlyRateMax);

            // Fetch data from API
            fetch(`/api/geniuses?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    // Hide loading message
                    document.getElementById('loadingMessage').style.display = 'none';

                    if (data.success) {
                        const geniuses = data.geniuses;
                        const geniusGrid = document.getElementById('geniusGrid');

                        // Clear existing genius cards (except loading and no results messages)
                        const existingCards = geniusGrid.querySelectorAll('.genius-card');
                        existingCards.forEach(card => card.remove());

                        if (geniuses.length === 0) {
                            // Show no results message
                            document.getElementById('noGeniusMessage').style.display = 'block';
                        } else {
                            // Hide no results message
                            document.getElementById('noGeniusMessage').style.display = 'none';

                            // Get the template
                            const template = document.getElementById('geniusCardTemplate');

                            // Create and append genius cards
                            geniuses.forEach(genius => {
                                const card = document.importNode(template.content, true).querySelector('.genius-card');

                                // Set badge based on expertise
                                const badge = card.querySelector('.expert-badge');
                                if (genius.expertise === 'Expert') {
                                    badge.textContent = 'EXPERT';
                                    badge.className = 'expert-badge';
                                } else if (genius.expertise === 'Intermediate') {
                                    badge.textContent = 'INTERMEDIATE';
                                    badge.className = 'expert-badge';
                                } else {
                                    badge.textContent = 'ENTRY LEVEL';
                                    badge.className = 'entry-badge';
                                }

                                // Set profile image
                                const img = card.querySelector('.genius-image img');
                                if (genius.has_profile_photo) {
                                    img.src = `/api/profile-photo/genius/${genius.id}`;
                                } else {
                                    img.src = "{{ url_for('static', filename='img/default-avatar.png') }}";
                                }
                                img.alt = `${genius.first_name} ${genius.last_name}`;

                                // Set name and details
                                card.querySelector('.genius-name').textContent = `${genius.first_name} ${genius.last_name}`;
                                card.querySelector('.position-value').textContent = genius.position || 'Not specified';
                                card.querySelector('.country-value').textContent = genius.country || 'Not specified';
                                card.querySelector('.rate-value').textContent = genius.hourly_rate ? `$${genius.hourly_rate}/hr` : 'Not specified';
                                card.querySelector('.expertise-value').textContent = genius.expertise || 'Not specified';
                                card.querySelector('.availability-value').textContent = genius.availability || 'Not specified';

                                // Set introduction (truncated version)
                                const introText = card.querySelector('.introduction-text');
                                const seeMoreBtn = card.querySelector('.see-more-btn');

                                // Create a truncated version (first 100 characters)
                                const truncatedIntro = genius.introduction ?
                                    (genius.introduction.length > 100 ?
                                        genius.introduction.substring(0, 100) + '...' :
                                        genius.introduction) :
                                    'No introduction available.';

                                // Set the truncated version initially
                                introText.textContent = truncatedIntro;
                                introText.setAttribute('data-genius-id', genius.id);

                                // Show the "See More" button only if text is long enough
                                const isLongText = (genius.introduction && genius.introduction.length > 100);

                                if (isLongText) {
                                    seeMoreBtn.style.display = 'inline-flex';

                                    // Add click event to fetch and display full text
                                    seeMoreBtn.onclick = function() {
                                        const isExpanded = introText.classList.contains('expanded');
                                        const geniusId = introText.getAttribute('data-genius-id');

                                        if (!isExpanded) {
                                            // Show loading indicator
                                            introText.textContent = 'Loading full introduction...';

                                            // Fetch the full introduction from the server
                                            fetch(`/api/genius/${geniusId}/introduction`)
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.success) {
                                                        // Show the full introduction
                                                        introText.textContent = data.introduction || 'No introduction available.';
                                                        introText.classList.add('expanded');
                                                        this.classList.add('expanded');
                                                        this.innerHTML = 'See Less <i class="fas fa-chevron-up"></i>';
                                                    } else {
                                                        // Show error message
                                                        introText.textContent = 'Could not load the full introduction.';
                                                        console.error('Error fetching introduction:', data.error);
                                                    }
                                                })
                                                .catch(error => {
                                                    introText.textContent = 'Could not load the full introduction.';
                                                    console.error('Error fetching introduction:', error);
                                                });
                                        } else {
                                            // Show truncated text
                                            introText.textContent = truncatedIntro;
                                            introText.classList.remove('expanded');
                                            this.classList.remove('expanded');
                                            this.innerHTML = 'See More <i class="fas fa-chevron-down"></i>';
                                        }
                                    };
                                } else {
                                    seeMoreBtn.style.display = 'none';
                                }

                                // Set action buttons
                                const viewProfileBtn = card.querySelector('.view-profile-btn');
                                viewProfileBtn.href = `#view-profile-${genius.id}`; // You can update this to a real profile page
                                viewProfileBtn.onclick = (e) => {
                                    e.preventDefault();
                                    alert(`View profile functionality for ${genius.first_name} ${genius.last_name} will be implemented soon.`);
                                };

                                const contactBtn = card.querySelector('.contact-genius-btn');
                                contactBtn.href = `#contact-${genius.id}`; // You can update this to a real contact page
                                contactBtn.onclick = (e) => {
                                    e.preventDefault();
                                    alert(`Contact functionality for ${genius.first_name} ${genius.last_name} will be implemented soon.`);
                                };

                                // Add the card to the grid
                                geniusGrid.appendChild(card);
                            });

                            // Trigger scroll handler to update filter position after loading profiles
                            setTimeout(() => {
                                // Make sure filter section is visible
                                const filtersElement = document.getElementById('filtersSection');
                                if (filtersElement) {
                                    filtersElement.style.display = 'block';
                                    console.log("Making filter section visible after loading profiles");
                                }
                                window.dispatchEvent(new Event('scroll'));
                            }, 300);
                        }
                    } else {
                        console.error('Error fetching genius profiles:', data.error);
                        document.getElementById('noGeniusMessage').style.display = 'block';
                        document.getElementById('noGeniusMessage').querySelector('h3').textContent = 'Error loading profiles';
                        document.getElementById('noGeniusMessage').querySelector('p').textContent = 'Please try again later.';
                    }
                })
                .catch(error => {
                    console.error('Error fetching genius profiles:', error);
                    document.getElementById('loadingMessage').style.display = 'none';
                    document.getElementById('noGeniusMessage').style.display = 'block';
                    document.getElementById('noGeniusMessage').querySelector('h3').textContent = 'Error loading profiles';
                    document.getElementById('noGeniusMessage').querySelector('p').textContent = 'Please try again later.';
                });
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize quote slider
            document.getElementById('currentQuote').textContent = quotes[currentQuoteIndex];

            // Auto-rotate quotes every 7 seconds
            setInterval(function() {
                changeQuote(1);
            }, 7000);

            // Initialize introduction section
            const introText = document.getElementById('introduction-text');
            const readMoreBtn = document.getElementById('read-more-btn');

            // Hide the button if the text is short enough
            if (introText.scrollHeight <= 100) {
                readMoreBtn.style.display = 'none';
            }

            // Initialize genius filters
            const countrySelect = document.getElementById('countrySelect');
            const expertiseSelect = document.getElementById('expertiseSelect');
            const availabilitySelect = document.getElementById('availabilitySelect');
            const hourlyRateMin = document.getElementById('hourlyRateMin');
            const hourlyRateMax = document.getElementById('hourlyRateMax');
            const resetFiltersBtn = document.getElementById('resetFiltersBtn');

            // Add event listeners to filter elements
            if (countrySelect) countrySelect.addEventListener('change', fetchGeniusProfiles);
            if (expertiseSelect) expertiseSelect.addEventListener('change', fetchGeniusProfiles);
            if (availabilitySelect) availabilitySelect.addEventListener('change', fetchGeniusProfiles);
            if (hourlyRateMin) hourlyRateMin.addEventListener('change', fetchGeniusProfiles);
            if (hourlyRateMax) hourlyRateMax.addEventListener('change', fetchGeniusProfiles);

            // Reset filters button
            if (resetFiltersBtn) {
                resetFiltersBtn.addEventListener('click', function() {
                    // Reset all select elements
                    document.querySelectorAll('.filter-select').forEach(select => {
                        select.selectedIndex = 0;
                    });

                    // Reset custom dropdown
                    const customTrigger = document.querySelector('.custom-select-trigger');
                    const customOptions = document.querySelectorAll('.custom-select-option');
                    if (customTrigger) {
                        customTrigger.textContent = 'All Countries';
                    }
                    if (customOptions.length > 0) {
                        customOptions.forEach(option => option.classList.remove('selected'));
                        customOptions[0].classList.add('selected'); // Select first option (All Countries)
                    }

                    // Reset hourly rate inputs
                    if (hourlyRateMin) hourlyRateMin.value = '';
                    if (hourlyRateMax) hourlyRateMax.value = '';

                    // Reset search
                    const searchInput = document.getElementById('geniusSearch');
                    if (searchInput) searchInput.value = '';

                    // Fetch profiles with reset filters
                    fetchGeniusProfiles();
                });
            }

            // Fetch genius profiles on page load
            fetchGeniusProfiles();

            // Enhanced sticky positioning for horizontal filters
            document.addEventListener('DOMContentLoaded', function() {
                const filtersElement = document.getElementById('filtersSection');
                const contentContainer = document.querySelector('.content-container');
                const navbar = document.querySelector('.navbar');
                const geniusGrid = document.getElementById('geniusGrid');

                if (filtersElement && contentContainer) {
                    console.log("Filter section found:", filtersElement);

                    // Make sure filter is visible initially
                    filtersElement.style.display = 'block';

                    // Get the original position of the filters section
                    let filtersOriginalPosition = filtersElement.getBoundingClientRect().top + window.pageYOffset;
                    console.log("Original position:", filtersOriginalPosition);

                    // Calculate initial positions
                    let navbarHeight = navbar ? navbar.offsetHeight : 60;
                    let contentRect = contentContainer.getBoundingClientRect();
                    let initialLeftOffset = contentRect.left;
                    let filtersWidth = contentRect.width;

                    // Function to handle scroll
                    function handleScroll() {
                        // Only apply on desktop and tablet
                        if (window.innerWidth < 768) {
                            filtersElement.style.position = 'static';
                            filtersElement.style.display = 'block';
                            filtersElement.style.width = '100%';
                            return;
                        }

                        // Get current scroll position
                        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                        // Always ensure filter is visible
                        filtersElement.style.display = 'block';

                        // Check if we've scrolled past the original position of the filters
                        if (scrollTop > filtersOriginalPosition - navbarHeight) {
                            // We've scrolled past the original position, make it fixed
                            filtersElement.style.position = 'fixed';
                            filtersElement.style.top = navbarHeight + 'px';
                            filtersElement.style.left = initialLeftOffset + 'px';
                            filtersElement.style.width = filtersWidth + 'px';
                            filtersElement.style.zIndex = '1000';

                            // Add sticky class for visual effects
                            filtersElement.classList.add('is-sticky');

                            // Add padding to the grid to prevent content jump
                            if (!document.getElementById('filterSpacer')) {
                                const spacer = document.createElement('div');
                                spacer.id = 'filterSpacer';
                                spacer.style.height = filtersElement.offsetHeight + 'px';
                                filtersElement.parentNode.insertBefore(spacer, document.querySelector('.genius-container'));
                            }
                        } else {
                            // We're above the original position, return to normal flow
                            filtersElement.style.position = 'static';
                            filtersElement.style.width = '100%';
                            filtersElement.style.zIndex = '';
                            filtersElement.classList.remove('is-sticky');

                            // Remove the spacer if it exists
                            const spacer = document.getElementById('filterSpacer');
                            if (spacer) {
                                spacer.remove();
                            }
                        }
                    }

                    // Function to handle resize
                    function handleResize() {
                        // Recalculate dimensions
                        navbarHeight = navbar ? navbar.offsetHeight : 60;
                        contentRect = contentContainer.getBoundingClientRect();
                        initialLeftOffset = contentRect.left;
                        filtersWidth = contentRect.width;

                        // Recalculate the original position (important for responsive layouts)
                        if (filtersElement.style.position !== 'fixed') {
                            filtersOriginalPosition = filtersElement.getBoundingClientRect().top + window.pageYOffset;
                        }

                        // Update filter position based on current scroll
                        handleScroll();
                    }

                    // Function to handle when genius profiles are loaded
                    function handleGeniusLoaded() {
                        console.log("Genius profiles loaded, updating filter position");
                        // Recalculate positions after genius profiles are loaded
                        setTimeout(handleScroll, 200);
                    }

                    // Add event listeners
                    window.addEventListener('scroll', handleScroll, { passive: true });
                    window.addEventListener('resize', handleResize, { passive: true });

                    // Listen for changes to the genius grid (when profiles are loaded)
                    if (geniusGrid) {
                        const observer = new MutationObserver(handleGeniusLoaded);
                        observer.observe(geniusGrid, { childList: true, subtree: true });
                    }

                    // Initial call to set correct position
                    setTimeout(handleScroll, 100);

                    // Additional calls to ensure filter is visible
                    setTimeout(handleScroll, 500);
                    setTimeout(handleScroll, 1000);
                }
            });

            // Initialize view toggle buttons
            const viewButtons = document.querySelectorAll('.view-btn');
            viewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    viewButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Add event listeners to filter selects
            const jobTypeSelect = document.getElementById('jobTypeSelect');
            const categorySelect = document.getElementById('categorySelect');
            const applicationsSelect = document.getElementById('applicationsSelect');
            const datePostedSelect = document.getElementById('datePostedSelect');
            const budgetInput = document.getElementById('budget');

            if (jobTypeSelect) {
                jobTypeSelect.addEventListener('change', applyJobFilters);
            }

            if (categorySelect) {
                categorySelect.addEventListener('change', applyJobFilters);
            }

            if (applicationsSelect) {
                applicationsSelect.addEventListener('change', applyJobFilters);
            }

            if (datePostedSelect) {
                datePostedSelect.addEventListener('change', applyJobFilters);
            }

            if (budgetInput) {
                budgetInput.addEventListener('input', applyJobFilters);
            }

            // Reset filters button for job filters
            const jobResetFiltersBtn = document.querySelector('.reset-filters-btn:not(#resetFiltersBtn)');
            if (jobResetFiltersBtn) {
                jobResetFiltersBtn.addEventListener('click', function() {
                    // Reset all select elements
                    document.querySelectorAll('.filter-select').forEach(select => {
                        select.selectedIndex = 0;
                    });

                    // Reset budget input
                    if (budgetInput) {
                        budgetInput.value = '';
                    }

                    // Reset search
                    const searchInput = document.getElementById('jobSearch');
                    if (searchInput) {
                        searchInput.value = '';
                        searchJobs();
                    } else {
                        applyJobFilters();
                    }
                });
            }

            // Initialize priority dropdown items
            const dropdownItems = document.querySelectorAll('.dropdown-item');
            const priorityHeader = document.querySelector('.priority-list-header h3');

            dropdownItems.forEach(item => {
                item.addEventListener('click', function() {
                    priorityHeader.textContent = this.textContent;
                    document.getElementById('priorityDropdown').classList.remove('active');
                    document.querySelector('.dropdown-arrow').style.transform = 'rotate(0)';
                });
            });

            // Mobile menu toggle
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const navLinks = document.getElementById('navLinks');

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    navLinks.classList.toggle('active');

                    // Change icon based on menu state
                    const icon = this.querySelector('i');
                    if (navLinks.classList.contains('active')) {
                        icon.classList.remove('fa-bars');
                        icon.classList.add('fa-times');
                    } else {
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    }
                });
            }

            // Mobile dropdown toggles
            const navDropdowns = document.querySelectorAll('.nav-dropdown');

            navDropdowns.forEach(dropdown => {
                const dropBtn = dropdown.querySelector('.nav-dropbtn');

                dropBtn.addEventListener('click', function(e) {
                    // Only handle click differently on mobile
                    if (window.innerWidth <= 992) {
                        e.preventDefault();
                        dropdown.classList.toggle('active');

                        // Change dropdown icon
                        const icon = this.querySelector('i');
                        if (dropdown.classList.contains('active')) {
                            icon.classList.remove('fa-chevron-down');
                            icon.classList.add('fa-chevron-up');
                        } else {
                            icon.classList.remove('fa-chevron-up');
                            icon.classList.add('fa-chevron-down');
                        }
                    }
                });
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 992 &&
                    !navLinks.contains(e.target) &&
                    !mobileMenuBtn.contains(e.target) &&
                    navLinks.classList.contains('active')) {
                    navLinks.classList.remove('active');

                    // Reset icon
                    const icon = mobileMenuBtn.querySelector('i');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 992 && navLinks.classList.contains('active')) {
                    navLinks.classList.remove('active');

                    // Reset icon
                    const icon = mobileMenuBtn.querySelector('i');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');

                    // Reset all dropdowns
                    navDropdowns.forEach(dropdown => {
                        dropdown.classList.remove('active');
                        const icon = dropdown.querySelector('.nav-dropbtn i');
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    });
                }
            });

            const searchInput = document.getElementById('searchInput');
        });

        document.addEventListener('DOMContentLoaded', function () {
            // Enhanced country dropdown implementation
            const countrySelectWrapper = document.querySelector('#filtersSection .filter-group .custom-select-wrapper');
            if (countrySelectWrapper) {
                const originalSelect = countrySelectWrapper.querySelector('#countrySelect');
                const customTrigger = countrySelectWrapper.querySelector('.custom-select-trigger');
                const customOptionsContainer = countrySelectWrapper.querySelector('.custom-select-options');

                // Ensure the container is properly cleared and populated
                if (originalSelect && customTrigger && customOptionsContainer) {
                    // Clear existing options
                    customOptionsContainer.innerHTML = '';

                    // Add the default "All Countries" option first
                    const defaultOption = document.createElement('div');
                    defaultOption.classList.add('custom-select-option', 'default-option', 'selected');
                    defaultOption.textContent = 'All Countries';
                    defaultOption.dataset.value = '';
                    customOptionsContainer.appendChild(defaultOption);

                    // Process optgroups and options
                    Array.from(originalSelect.children).forEach(child => {
                        if (child.tagName === 'OPTGROUP') {
                            const continent = child.getAttribute('label');

                            // Create continent header
                            const continentHeader = document.createElement('div');
                            continentHeader.classList.add('custom-select-continent');
                            continentHeader.textContent = continent;
                            customOptionsContainer.appendChild(continentHeader);

                            // Add countries for this continent
                            Array.from(child.children).forEach(option => {
                                if (option.tagName === 'OPTION' && option.value) {
                                    const customOption = document.createElement('div');
                                    customOption.classList.add('custom-select-option');
                                    customOption.textContent = option.textContent;
                                    customOption.dataset.value = option.value;
                                    customOptionsContainer.appendChild(customOption);
                                }
                            });
                        }
                    });

                    // Toggle dropdown visibility
                    customTrigger.addEventListener('click', function(event) {
                        event.preventDefault();
                        event.stopPropagation();

                        // Close other dropdowns first
                        document.querySelectorAll('.custom-select-wrapper.open').forEach(wrapper => {
                            if (wrapper !== countrySelectWrapper) {
                                wrapper.classList.remove('open');
                            }
                        });

                        // Toggle dropdown
                        const isOpen = countrySelectWrapper.classList.contains('open');

                        if (!isOpen) {
                            // Position the dropdown
                            const triggerRect = customTrigger.getBoundingClientRect();
                            customOptionsContainer.style.top = (triggerRect.bottom + 5) + 'px';
                            customOptionsContainer.style.left = triggerRect.left + 'px';
                            customOptionsContainer.style.width = triggerRect.width + 'px';

                            countrySelectWrapper.classList.add('open');
                        } else {
                            countrySelectWrapper.classList.remove('open');
                        }
                    });

                    // Handle option selection
                    customOptionsContainer.addEventListener('click', function(event) {
                        event.stopPropagation();
                        const clickedOption = event.target.closest('.custom-select-option');

                        if (clickedOption && !clickedOption.classList.contains('custom-select-continent')) {
                            // Update the visible trigger text
                            customTrigger.textContent = clickedOption.textContent;

                            // Update the hidden select value
                            originalSelect.value = clickedOption.dataset.value;

                            // Update selected class
                            customOptionsContainer.querySelectorAll('.custom-select-option').forEach(opt => {
                                opt.classList.remove('selected');
                            });
                            clickedOption.classList.add('selected');

                            // Close dropdown
                            countrySelectWrapper.classList.remove('open');

                            // Trigger change event for filtering
                            const changeEvent = new Event('change', { bubbles: true });
                            originalSelect.dispatchEvent(changeEvent);

                            // Apply filters
                            if (typeof fetchGeniusProfiles === 'function') {
                                fetchGeniusProfiles();
                            } else if (typeof searchGenius === 'function') {
                                searchGenius();
                            }
                        }
                    });

                    // Prevent dropdown from closing when clicking on continent headers
                    customOptionsContainer.addEventListener('click', function(event) {
                        if (event.target.classList.contains('custom-select-continent')) {
                            event.stopPropagation();
                        }
                    });
                }
            }

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (countrySelectWrapper && !countrySelectWrapper.contains(event.target)) {
                    countrySelectWrapper.classList.remove('open');
                }
            });

            // Close dropdown on scroll or resize
            window.addEventListener('scroll', function() {
                if (countrySelectWrapper) {
                    countrySelectWrapper.classList.remove('open');
                }
            });

            window.addEventListener('resize', function() {
                if (countrySelectWrapper) {
                    countrySelectWrapper.classList.remove('open');
                }
            });

            // Enhanced expertise dropdown implementation
            const expertiseSelectWrapper = document.querySelector('#filtersSection .filter-group:nth-child(2) .custom-select-wrapper');
            if (expertiseSelectWrapper) {
                const originalSelect = expertiseSelectWrapper.querySelector('#expertiseSelect');
                const customTrigger = expertiseSelectWrapper.querySelector('.custom-select-trigger');
                const customOptionsContainer = expertiseSelectWrapper.querySelector('.custom-select-options');

                // Ensure the container is properly cleared and populated
                if (originalSelect && customTrigger && customOptionsContainer) {
                    // Clear existing options
                    customOptionsContainer.innerHTML = '';

                    // Add options from the original select
                    Array.from(originalSelect.children).forEach(option => {
                        const customOption = document.createElement('div');
                        customOption.classList.add('custom-select-option');
                        if (option.value === '') {
                            customOption.classList.add('default-option', 'selected');
                        }
                        customOption.textContent = option.textContent;
                        customOption.dataset.value = option.value;
                        customOptionsContainer.appendChild(customOption);
                    });

                    // Toggle dropdown visibility
                    customTrigger.addEventListener('click', function(event) {
                        event.preventDefault();
                        event.stopPropagation();

                        // Close other dropdowns first
                        document.querySelectorAll('.custom-select-wrapper.open').forEach(wrapper => {
                            if (wrapper !== expertiseSelectWrapper) {
                                wrapper.classList.remove('open');
                            }
                        });

                        // Toggle dropdown
                        const isOpen = expertiseSelectWrapper.classList.contains('open');

                        if (!isOpen) {
                            // Position the dropdown
                            const triggerRect = customTrigger.getBoundingClientRect();
                            customOptionsContainer.style.top = (triggerRect.bottom + 5) + 'px';
                            customOptionsContainer.style.left = triggerRect.left + 'px';
                            customOptionsContainer.style.width = triggerRect.width + 'px';

                            expertiseSelectWrapper.classList.add('open');
                        } else {
                            expertiseSelectWrapper.classList.remove('open');
                        }
                    });

                    // Handle option selection
                    customOptionsContainer.addEventListener('click', function(event) {
                        event.stopPropagation();
                        const clickedOption = event.target.closest('.custom-select-option');

                        if (clickedOption) {
                            // Update the visible trigger text
                            customTrigger.textContent = clickedOption.textContent;

                            // Update the hidden select value
                            originalSelect.value = clickedOption.dataset.value;

                            // Update selected class
                            customOptionsContainer.querySelectorAll('.custom-select-option').forEach(opt => {
                                opt.classList.remove('selected');
                            });
                            clickedOption.classList.add('selected');

                            // Close dropdown
                            expertiseSelectWrapper.classList.remove('open');

                            // Trigger change event for filtering
                            const changeEvent = new Event('change', { bubbles: true });
                            originalSelect.dispatchEvent(changeEvent);

                            // Apply filters
                            if (typeof fetchGeniusProfiles === 'function') {
                                fetchGeniusProfiles();
                            }
                        }
                    });
                }
            }

            // Close expertise dropdown when clicking outside, scrolling, or resizing
            document.addEventListener('click', function(event) {
                if (expertiseSelectWrapper && !expertiseSelectWrapper.contains(event.target)) {
                    expertiseSelectWrapper.classList.remove('open');
                }
            });

            window.addEventListener('scroll', function() {
                if (expertiseSelectWrapper) {
                    expertiseSelectWrapper.classList.remove('open');
                }
            });

            window.addEventListener('resize', function() {
                if (expertiseSelectWrapper) {
                    expertiseSelectWrapper.classList.remove('open');
                }
            });

            // Enhanced availability dropdown implementation
            const availabilitySelectWrapper = document.querySelector('#filtersSection .filter-group:nth-child(3) .custom-select-wrapper');
            if (availabilitySelectWrapper) {
                const originalSelect = availabilitySelectWrapper.querySelector('#availabilitySelect');
                const customTrigger = availabilitySelectWrapper.querySelector('.custom-select-trigger');
                const customOptionsContainer = availabilitySelectWrapper.querySelector('.custom-select-options');

                // Ensure the container is properly cleared and populated
                if (originalSelect && customTrigger && customOptionsContainer) {
                    // Clear existing options
                    customOptionsContainer.innerHTML = '';

                    // Add options from the original select
                    Array.from(originalSelect.children).forEach(option => {
                        const customOption = document.createElement('div');
                        customOption.classList.add('custom-select-option');
                        if (option.value === '') {
                            customOption.classList.add('default-option', 'selected');
                        }
                        customOption.textContent = option.textContent;
                        customOption.dataset.value = option.value;
                        customOptionsContainer.appendChild(customOption);
                    });

                    // Toggle dropdown visibility
                    customTrigger.addEventListener('click', function(event) {
                        event.preventDefault();
                        event.stopPropagation();

                        // Close other dropdowns first
                        document.querySelectorAll('.custom-select-wrapper.open').forEach(wrapper => {
                            if (wrapper !== availabilitySelectWrapper) {
                                wrapper.classList.remove('open');
                            }
                        });

                        // Toggle dropdown
                        const isOpen = availabilitySelectWrapper.classList.contains('open');

                        if (!isOpen) {
                            // Position the dropdown
                            const triggerRect = customTrigger.getBoundingClientRect();
                            customOptionsContainer.style.top = (triggerRect.bottom + 5) + 'px';
                            customOptionsContainer.style.left = triggerRect.left + 'px';
                            customOptionsContainer.style.width = triggerRect.width + 'px';

                            availabilitySelectWrapper.classList.add('open');
                        } else {
                            availabilitySelectWrapper.classList.remove('open');
                        }
                    });

                    // Handle option selection
                    customOptionsContainer.addEventListener('click', function(event) {
                        event.stopPropagation();
                        const clickedOption = event.target.closest('.custom-select-option');

                        if (clickedOption) {
                            // Update the visible trigger text
                            customTrigger.textContent = clickedOption.textContent;

                            // Update the hidden select value
                            originalSelect.value = clickedOption.dataset.value;

                            // Update selected class
                            customOptionsContainer.querySelectorAll('.custom-select-option').forEach(opt => {
                                opt.classList.remove('selected');
                            });
                            clickedOption.classList.add('selected');

                            // Close dropdown
                            availabilitySelectWrapper.classList.remove('open');

                            // Trigger change event for filtering
                            const changeEvent = new Event('change', { bubbles: true });
                            originalSelect.dispatchEvent(changeEvent);

                            // Apply filters
                            if (typeof fetchGeniusProfiles === 'function') {
                                fetchGeniusProfiles();
                            }
                        }
                    });
                }
            }

            // Close availability dropdown when clicking outside, scrolling, or resizing
            document.addEventListener('click', function(event) {
                if (availabilitySelectWrapper && !availabilitySelectWrapper.contains(event.target)) {
                    availabilitySelectWrapper.classList.remove('open');
                }
            });

            window.addEventListener('scroll', function() {
                if (availabilitySelectWrapper) {
                    availabilitySelectWrapper.classList.remove('open');
                }
            });

            window.addEventListener('resize', function() {
                if (availabilitySelectWrapper) {
                    availabilitySelectWrapper.classList.remove('open');
                }
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Close dropdowns when clicking outside
            window.addEventListener('click', function(e) {
                if (!e.target.matches('.nav-dropbtn')) {
                    const dropdowns = document.getElementsByClassName('nav-dropdown-content');
                    for (let dropdown of dropdowns) {
                        if (dropdown.classList.contains('show')) {
                            dropdown.classList.remove('show');
                        }
                    }
                }
            });
        });
        document.addEventListener('DOMContentLoaded', function() {
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileButton = profileDropdown.querySelector('.profile-button');
            const notificationIcon = document.querySelector('.notification-icon');

            // Toggle notification dropdown on notification icon click
            notificationIcon.addEventListener('click', function(e) {
                e.stopPropagation();
                const notificationDropdown = this.querySelector('.notification-dropdown-content');
                if (notificationDropdown.style.display === 'block') {
                    notificationDropdown.style.display = 'none';
                } else {
                    notificationDropdown.style.display = 'block';

                    // Position the dropdown properly
                    const iconRect = notificationIcon.getBoundingClientRect();
                    notificationDropdown.style.top = (iconRect.height + 5) + 'px';

                    // Ensure dropdown is within viewport
                    const dropdownRect = notificationDropdown.getBoundingClientRect();
                    if (dropdownRect.right > window.innerWidth) {
                        const overflowAmount = dropdownRect.right - window.innerWidth;
                        notificationDropdown.style.right = overflowAmount + 10 + 'px';
                    }
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdown.classList.remove('active');
                }
                if (!notificationIcon.contains(e.target)) {
                    const notificationDropdown = notificationIcon.querySelector('.notification-dropdown-content');
                    if (notificationDropdown) {
                        notificationDropdown.style.display = 'none';
                    }
                }
            });

            // Prevent dropdown from closing when clicking inside it
            notificationDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            // Mark all as read functionality
            const markAllReadBtn = document.querySelector('.mark-all-read');
            if (markAllReadBtn) {
                markAllReadBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const unreadItems = document.querySelectorAll('.notification-item.unread');
                    unreadItems.forEach(item => {
                        item.classList.remove('unread');
                    });
                    const badge = document.querySelector('.notification-badge');
                    if (badge) {
                        badge.textContent = '0';
                    }
                });
            }
        });
        // Simple notification dropdown toggle
        document.addEventListener('DOMContentLoaded', function() {
            const notificationIcon = document.querySelector('.notification-icon');
            const dropdown = document.querySelector('.notification-dropdown');

            notificationIcon.addEventListener('click', function(e) {
                e.stopPropagation();
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            });

            document.addEventListener('click', function(e) {
                if (!notificationIcon.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });

            // Mark all as read
            const markAllBtn = document.querySelector('.notification-header a');
            markAllBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                document.querySelectorAll('.notification-item.unread').forEach(item => {
                    item.classList.remove('unread');
                });

                document.querySelector('.notification-badge').textContent = '0';
            });
        });
        // Notification system for job applications
        document.addEventListener('DOMContentLoaded', function() {
            // Load notifications when page loads
            loadNotifications();

            // Function to load notifications
            function loadNotifications() {
                fetch('/get_client_notifications')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const notifications = data.notifications;
                            const notificationList = document.getElementById('notification-list');
                            const emptyNotifications = document.getElementById('empty-notifications');
                            const notificationCount = document.getElementById('notification-count');

                            // Clear existing notifications
                            notificationList.innerHTML = '';

                            if (notifications.length === 0) {
                                emptyNotifications.style.display = 'block';
                                notificationCount.textContent = '0';
                                return;
                            }

                            // Hide empty state
                            emptyNotifications.style.display = 'none';

                            // Count unread notifications
                            let unreadCount = 0;

                            // Add each notification to the list
                            notifications.forEach(notification => {
                                if (notification.status === 'pending') {
                                    unreadCount++;
                                }

                                const notificationItem = document.createElement('div');
                                notificationItem.className = `notification-item ${notification.status === 'pending' ? 'unread' : ''}`;
                                notificationItem.setAttribute('data-id', notification.id);

                                // Create notification content with enhanced styling
                                let notificationContent = `
                                    <div class="notification-icon-wrapper">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p><strong>${notification.first_name} ${notification.last_name}</strong> applied for your job: <strong>${notification.job_title}</strong></p>
                                        <span>${formatTimeAgo(notification.created_at)}</span>
                                `;

                                // Add action buttons for pending applications
                                if (notification.status === 'pending') {
                                    notificationContent += `
                                        <div class="notification-actions">
                                            <button class="accept-btn" data-id="${notification.id}">
                                                <i class="fas fa-check" style="margin-right: 5px;"></i> Accept
                                            </button>
                                            <button class="reject-btn" data-id="${notification.id}">
                                                <i class="fas fa-times" style="margin-right: 5px;"></i> Decline
                                            </button>
                                        </div>
                                    `;
                                } else if (notification.status === 'accept') {
                                    notificationContent += `
                                        <div class="notification-status accepted">
                                            <i class="fas fa-check-circle" style="margin-right: 4px;"></i> Accepted
                                        </div>
                                    `;
                                } else if (notification.status === 'reject') {
                                    notificationContent += `
                                        <div class="notification-status rejected">
                                            <i class="fas fa-times-circle" style="margin-right: 4px;"></i> Declined
                                        </div>
                                    `;
                                }

                                notificationContent += `</div>`;
                                notificationItem.innerHTML = notificationContent;

                                // Add click event to view application details
                                notificationItem.addEventListener('click', function(e) {
                                    // Don't navigate if clicking on buttons
                                    if (e.target.closest('.notification-actions')) {
                                        return;
                                    }
                                    window.location.href = `/view_application/${notification.id}`;
                                });

                                notificationList.appendChild(notificationItem);
                            });

                            // Add event listeners for accept/reject buttons
                            document.querySelectorAll('.accept-btn').forEach(button => {
                                button.addEventListener('click', function(e) {
                                    e.stopPropagation();
                                    const applicationId = this.getAttribute('data-id');
                                    handleApplication(applicationId, 'accept');
                                });
                            });

                            document.querySelectorAll('.reject-btn').forEach(button => {
                                button.addEventListener('click', function(e) {
                                    e.stopPropagation();
                                    const applicationId = this.getAttribute('data-id');
                                    handleApplication(applicationId, 'reject');
                                });
                            });

                            // Update notification count
                            notificationCount.textContent = unreadCount;

                        } else {
                            console.error('Error loading notifications:', data.error);
                            emptyNotifications.style.display = 'block';
                            emptyNotifications.innerHTML = '<i class="fas fa-exclamation-circle"></i><p>Error loading notifications</p>';
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching notifications:', error);
                        emptyNotifications.style.display = 'block';
                        emptyNotifications.innerHTML = '<i class="fas fa-exclamation-circle"></i><p>Error loading notifications</p>';
                    });
            }

            // Function to handle accepting or rejecting an application
            function handleApplication(applicationId, action) {
                fetch(`/handle-application/${applicationId}/${action}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Find the notification item and update its status immediately in the UI
                        const notificationItem = document.querySelector(`.notification-item[data-id="${applicationId}"]`);
                        if (notificationItem) {
                            // Remove unread class
                            notificationItem.classList.remove('unread');

                            // Remove action buttons
                            const actionsDiv = notificationItem.querySelector('.notification-actions');
                            if (actionsDiv) {
                                actionsDiv.remove();
                            }

                            // Add status indicator
                            const contentDiv = notificationItem.querySelector('.notification-content');
                            if (contentDiv) {
                                const statusDiv = document.createElement('div');
                                statusDiv.className = `notification-status ${action === 'accept' ? 'accepted' : 'rejected'}`;

                                if (action === 'accept') {
                                    statusDiv.innerHTML = '<i class="fas fa-check-circle" style="margin-right: 4px;"></i> Accepted';
                                } else {
                                    statusDiv.innerHTML = '<i class="fas fa-times-circle" style="margin-right: 4px;"></i> Declined';
                                }

                                contentDiv.appendChild(statusDiv);
                            }
                        }

                        // Update notification count
                        const currentCount = parseInt(document.getElementById('notification-count').textContent);
                        if (currentCount > 0) {
                            document.getElementById('notification-count').textContent = currentCount - 1;
                        }

                        // Show success message
                        const message = action === 'accept' ?
                            'Application accepted! A welcome message has been sent to the genius.' :
                            'Application declined.';

                        showToast(message, 'success');

                        // Optionally reload all notifications after a short delay
                        setTimeout(() => {
                            loadNotifications();
                        }, 2000);
                    } else {
                        console.error('Error handling application:', data.error);
                        showToast('Error: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('An error occurred. Please try again.', 'error');
                });
            }

            // Helper function to show notification toast
            function showNotification(message, type) {
                showToast(message, type);
            }

            // Helper function to show toast
            function showToast(message, type) {
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;

                // Add icon based on type
                let icon = '';
                if (type === 'success') {
                    icon = '<i class="fas fa-check-circle"></i>';
                } else if (type === 'error') {
                    icon = '<i class="fas fa-exclamation-circle"></i>';
                }

                toast.innerHTML = `${icon} <span style="margin-left: 10px;">${message}</span>`;

                document.body.appendChild(toast);

                // Show the toast
                setTimeout(() => {
                    toast.classList.add('show');
                }, 10);

                // Hide and remove the toast after 3 seconds
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 300);
                }, 3000);
            }

            // Helper function to format time ago
            function formatTimeAgo(dateString) {
                const date = new Date(dateString);
                const now = new Date();
                const diffInSeconds = Math.floor((now - date) / 1000);

                if (diffInSeconds < 60) {
                    return 'just now';
                } else if (diffInSeconds < 3600) {
                    const minutes = Math.floor(diffInSeconds / 60);
                    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
                } else if (diffInSeconds < 86400) {
                    const hours = Math.floor(diffInSeconds / 3600);
                    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
                } else {
                    const days = Math.floor(diffInSeconds / 86400);
                    return `${days} day${days > 1 ? 's' : ''} ago`;
                }
            }

            // Profile dropdown functionality
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileButton = profileDropdown.querySelector('.profile-button');

            // Toggle dropdown on profile button click
            profileButton.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdown.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdown.classList.remove('active');
                }
            });
        });
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handler for paused contracts section
            const pausedCard = document.querySelector('.overview-card[onclick="toggleEmptyState(\'paused\')"]');
            if (pausedCard) {
                console.log('Paused contracts section initialized');
            }
        });
    </script>
</body>
</html>
